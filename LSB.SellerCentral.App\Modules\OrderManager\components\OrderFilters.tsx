import React, { useState } from 'react';
import { OrderFilter } from '../types/order.types';
import { orderService } from '../services/orderService';

interface OrderFiltersProps {
  filters: OrderFilter;
  onFilterChange: (filters: Partial<OrderFilter>) => void;
  loading: boolean;
}

export const OrderFilters: React.FC<OrderFiltersProps> = ({
  filters,
  onFilterChange,
  loading
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const statusOptions = orderService.getOrderStatusOptions();
  const fulfillmentOptions = orderService.getFulfillmentChannelOptions();

  const handleInputChange = (field: keyof OrderFilter, value: any) => {
    onFilterChange({ [field]: value });
  };

  const handleDateChange = (field: 'startDate' | 'endDate', value: string) => {
    onFilterChange({ [field]: value || undefined });
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    onFilterChange({ searchTerm: value || undefined });
  };

  const handleClearFilters = () => {
    onFilterChange({
      startDate: undefined,
      endDate: undefined,
      orderStatus: undefined,
      fulfillmentChannel: undefined,
      searchTerm: undefined,
      hasTracking: undefined
    });
  };

  const hasActiveFilters = !!(
    filters.startDate ||
    filters.endDate ||
    filters.orderStatus ||
    filters.fulfillmentChannel ||
    filters.searchTerm ||
    filters.hasTracking !== undefined
  );

  const formatDateForInput = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toISOString().split('T')[0];
  };

  return (
    <div className="order-filters">
      <div className="filters-header">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search by Order ID, Customer Email, or Tracking Number..."
            value={filters.searchTerm || ''}
            onChange={handleSearchChange}
            className="search-input"
            disabled={loading}
          />
        </div>
        
        <div className="filter-actions">
          <button
            type="button"
            onClick={() => setIsExpanded(!isExpanded)}
            className={`expand-filters-button ${isExpanded ? 'expanded' : ''}`}
            disabled={loading}
          >
            {isExpanded ? 'Hide Filters' : 'Show Filters'}
            <span className="expand-icon">{isExpanded ? '▲' : '▼'}</span>
          </button>
          
          {hasActiveFilters && (
            <button
              type="button"
              onClick={handleClearFilters}
              className="clear-filters-button"
              disabled={loading}
            >
              Clear Filters
            </button>
          )}
        </div>
      </div>

      {isExpanded && (
        <div className="filters-expanded">
          <div className="filter-row">
            <div className="filter-group">
              <label htmlFor="startDate">Start Date:</label>
              <input
                id="startDate"
                type="date"
                value={formatDateForInput(filters.startDate)}
                onChange={(e) => handleDateChange('startDate', e.target.value)}
                className="date-input"
                disabled={loading}
              />
            </div>

            <div className="filter-group">
              <label htmlFor="endDate">End Date:</label>
              <input
                id="endDate"
                type="date"
                value={formatDateForInput(filters.endDate)}
                onChange={(e) => handleDateChange('endDate', e.target.value)}
                className="date-input"
                disabled={loading}
              />
            </div>

            <div className="filter-group">
              <label htmlFor="orderStatus">Order Status:</label>
              <select
                id="orderStatus"
                value={filters.orderStatus || ''}
                onChange={(e) => handleInputChange('orderStatus', e.target.value || undefined)}
                className="select-input"
                disabled={loading}
              >
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="fulfillmentChannel">Fulfillment:</label>
              <select
                id="fulfillmentChannel"
                value={filters.fulfillmentChannel || ''}
                onChange={(e) => handleInputChange('fulfillmentChannel', e.target.value || undefined)}
                className="select-input"
                disabled={loading}
              >
                {fulfillmentOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="filter-row">
            <div className="filter-group">
              <label htmlFor="hasTracking">Tracking Status:</label>
              <select
                id="hasTracking"
                value={filters.hasTracking === undefined ? '' : filters.hasTracking.toString()}
                onChange={(e) => {
                  const value = e.target.value;
                  handleInputChange('hasTracking', value === '' ? undefined : value === 'true');
                }}
                className="select-input"
                disabled={loading}
              >
                <option value="">All Orders</option>
                <option value="true">With Tracking</option>
                <option value="false">Without Tracking</option>
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="pageSize">Page Size:</label>
              <select
                id="pageSize"
                value={filters.pageSize}
                onChange={(e) => handleInputChange('pageSize', parseInt(e.target.value))}
                className="select-input"
                disabled={loading}
              >
                <option value={25}>25 per page</option>
                <option value={50}>50 per page</option>
                <option value={100}>100 per page</option>
                <option value={200}>200 per page</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Quick Filters:</label>
              <div className="quick-filters">
                <button
                  type="button"
                  onClick={() => handleDateChange('startDate', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])}
                  className="quick-filter-button"
                  disabled={loading}
                >
                  Last 7 Days
                </button>
                <button
                  type="button"
                  onClick={() => handleDateChange('startDate', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])}
                  className="quick-filter-button"
                  disabled={loading}
                >
                  Last 30 Days
                </button>
                <button
                  type="button"
                  onClick={() => handleInputChange('hasTracking', false)}
                  className="quick-filter-button"
                  disabled={loading}
                >
                  Missing Tracking
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {hasActiveFilters && (
        <div className="active-filters">
          <span className="active-filters-label">Active Filters:</span>
          <div className="active-filter-tags">
            {filters.startDate && (
              <span className="filter-tag">
                From: {new Date(filters.startDate).toLocaleDateString()}
                <button onClick={() => handleDateChange('startDate', '')}>×</button>
              </span>
            )}
            {filters.endDate && (
              <span className="filter-tag">
                To: {new Date(filters.endDate).toLocaleDateString()}
                <button onClick={() => handleDateChange('endDate', '')}>×</button>
              </span>
            )}
            {filters.orderStatus && (
              <span className="filter-tag">
                Status: {filters.orderStatus}
                <button onClick={() => handleInputChange('orderStatus', undefined)}>×</button>
              </span>
            )}
            {filters.fulfillmentChannel && (
              <span className="filter-tag">
                Fulfillment: {filters.fulfillmentChannel}
                <button onClick={() => handleInputChange('fulfillmentChannel', undefined)}>×</button>
              </span>
            )}
            {filters.hasTracking !== undefined && (
              <span className="filter-tag">
                {filters.hasTracking ? 'With Tracking' : 'Without Tracking'}
                <button onClick={() => handleInputChange('hasTracking', undefined)}>×</button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderFilters;
