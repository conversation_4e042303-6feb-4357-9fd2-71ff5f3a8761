# LSB.SellerCentral.Core

This is the shared core library for Amazon Seller Partner API integration in the LSB Seller Central application.

## Purpose

This library provides a unified interface for interacting with Amazon's Seller Partner API (SP-API) across all components of the LSB Seller Central system.

## Features

- **SP-API Configuration**: Centralized configuration for Amazon SP-API credentials and endpoints
- **Authentication**: LWA (Login with Amazon) authentication handling
- **API Clients**: Wrapper classes for various SP-API endpoints
- **Models**: Shared data models for SP-API responses
- **Error Handling**: Standardized error handling and retry logic

## Structure

- **Configuration**: SP-API configuration classes
- **Authentication**: LWA authentication services
- **Clients**: API client wrappers for different SP-API endpoints
- **Models**: Data transfer objects and response models
- **Exceptions**: Custom exception classes for SP-API errors

## Dependencies

- .NET 9.0
- software.amzn.spapi v1.1.2 (Official Amazon SP-API SDK)

## Usage

This library is referenced by:
- LSB.SellerCentral.BackEnd (Web API)
- LSB.SellerCentral.Services (Windows Services for data collection)

## Getting Started

1. Build the library: `dotnet build`
2. Configure SP-API credentials in your application
3. Reference this library in your projects