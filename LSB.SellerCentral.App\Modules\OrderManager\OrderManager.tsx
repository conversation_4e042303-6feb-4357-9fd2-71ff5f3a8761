import React, { useState, useEffect } from 'react';
import { OrderTable } from './components/OrderTable';
import { OrderFilters } from './components/OrderFilters';
import { SyncButton } from './components/SyncButton';
import { orderService } from './services/orderService';
import { Order, OrderFilter, PaginatedOrders } from './types/order.types';

interface OrderManagerProps {
  className?: string;
}

export const OrderManager: React.FC<OrderManagerProps> = ({ className = '' }) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 50,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });
  
  const [filters, setFilters] = useState<OrderFilter>({
    page: 1,
    pageSize: 50
  });

  const [syncing, setSyncing] = useState(false);

  // Load orders when component mounts or filters change
  useEffect(() => {
    loadOrders();
  }, [filters]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result: PaginatedOrders = await orderService.getOrders(filters);
      
      setOrders(result.orders);
      setPagination({
        page: result.page,
        pageSize: result.pageSize,
        totalCount: result.totalCount,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPreviousPage: result.hasPreviousPage
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load orders');
      console.error('Error loading orders:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (newFilters: Partial<OrderFilter>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1 // Reset to first page when filters change
    }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({
      ...prev,
      page: newPage
    }));
  };

  const handleTrackingUpdate = async (orderId: number, trackingNumber: string, carrierName?: string) => {
    try {
      await orderService.updateTracking(orderId, {
        trackingNumber,
        carrierName,
        shipmentDate: new Date().toISOString()
      });
      
      // Refresh the orders list
      await loadOrders();
      
      // Show success message (you might want to use a toast notification here)
      console.log('Tracking updated successfully');
    } catch (err) {
      console.error('Error updating tracking:', err);
      setError(err instanceof Error ? err.message : 'Failed to update tracking');
    }
  };

  const handleSync = async (orderIds?: string[]) => {
    try {
      setSyncing(true);
      setError(null);
      
      const syncData = orderIds ? { orderIds } : { syncFromDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString() };
      const result = await orderService.syncOrders(syncData);
      
      if (result.success) {
        // Refresh the orders list after sync
        await loadOrders();
        console.log('Sync completed:', result.message);
      } else {
        setError(result.message || 'Sync failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sync orders');
      console.error('Error syncing orders:', err);
    } finally {
      setSyncing(false);
    }
  };

  const handleRefreshOrder = async (orderId: number) => {
    try {
      await orderService.getOrderById(orderId, true); // refresh = true
      await loadOrders(); // Refresh the list
    } catch (err) {
      console.error('Error refreshing order:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh order');
    }
  };

  return (
    <div className={`order-manager ${className}`}>
      <div className="order-manager-header">
        <h1>Order Manager</h1>
        <div className="header-actions">
          <SyncButton 
            onSync={handleSync}
            syncing={syncing}
          />
        </div>
      </div>

      {error && (
        <div className="error-message">
          <span>⚠️ {error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <OrderFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        loading={loading}
      />

      <div className="order-stats">
        <div className="stat-item">
          <span className="stat-label">Total Orders:</span>
          <span className="stat-value">{pagination.totalCount}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Current Page:</span>
          <span className="stat-value">{pagination.page} of {pagination.totalPages}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Orders with Tracking:</span>
          <span className="stat-value">
            {orders.filter(o => o.trackingNumber).length} / {orders.length}
          </span>
        </div>
      </div>

      <OrderTable
        orders={orders}
        loading={loading}
        onTrackingUpdate={handleTrackingUpdate}
        onRefreshOrder={handleRefreshOrder}
        pagination={pagination}
        onPageChange={handlePageChange}
      />
    </div>
  );
};

export default OrderManager;
