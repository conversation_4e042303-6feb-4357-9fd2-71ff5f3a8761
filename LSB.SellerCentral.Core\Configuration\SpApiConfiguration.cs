using System.ComponentModel.DataAnnotations;

namespace LSB.SellerCentral.Core.Configuration;

/// <summary>
/// Configuration settings for Amazon Seller Partner API integration
/// </summary>
public class SpApiConfiguration
{
    /// <summary>
    /// Configuration section name in appsettings.json
    /// </summary>
    public const string SectionName = "SpApi";

    /// <summary>
    /// LWA (Login with Amazon) Client ID
    /// </summary>
    [Required]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// LWA (Login with Amazon) Client Secret
    /// </summary>
    [Required]
    public string ClientSecret { get; set; } = string.Empty;

    /// <summary>
    /// LWA Refresh Token for the selling partner
    /// </summary>
    [Required]
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// SP-API endpoint region (e.g., "https://sellingpartnerapi-na.amazon.com")
    /// </summary>
    [Required]
    public string ApiEndpoint { get; set; } = string.Empty;

    /// <summary>
    /// LWA token endpoint (typically "https://api.amazon.com/auth/o2/token")
    /// </summary>
    [Required]
    public string LwaEndpoint { get; set; } = "https://api.amazon.com/auth/o2/token";

    /// <summary>
    /// AWS Region for the SP-API endpoint (e.g., "us-east-1")
    /// </summary>
    [Required]
    public string AwsRegion { get; set; } = string.Empty;

    /// <summary>
    /// Marketplace IDs that this application will work with
    /// </summary>
    public List<string> MarketplaceIds { get; set; } = new();

    /// <summary>
    /// Default timeout for API requests in seconds
    /// </summary>
    public int RequestTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Maximum number of retry attempts for failed requests
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Base delay between retry attempts in milliseconds
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// Whether to use exponential backoff for retries
    /// </summary>
    public bool UseExponentialBackoff { get; set; } = true;

    /// <summary>
    /// Application name for user agent string
    /// </summary>
    public string ApplicationName { get; set; } = "LSB.SellerCentral";

    /// <summary>
    /// Application version for user agent string
    /// </summary>
    public string ApplicationVersion { get; set; } = "1.0.0";

    /// <summary>
    /// Validates the configuration settings
    /// </summary>
    /// <returns>True if configuration is valid, false otherwise</returns>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(ClientId) &&
               !string.IsNullOrWhiteSpace(ClientSecret) &&
               !string.IsNullOrWhiteSpace(RefreshToken) &&
               !string.IsNullOrWhiteSpace(ApiEndpoint) &&
               !string.IsNullOrWhiteSpace(LwaEndpoint) &&
               !string.IsNullOrWhiteSpace(AwsRegion) &&
               MarketplaceIds.Count > 0 &&
               RequestTimeoutSeconds > 0 &&
               MaxRetryAttempts >= 0 &&
               RetryDelayMs > 0;
    }

    /// <summary>
    /// Gets the primary marketplace ID (first in the list)
    /// </summary>
    public string PrimaryMarketplaceId => MarketplaceIds.FirstOrDefault() ?? string.Empty;
}
