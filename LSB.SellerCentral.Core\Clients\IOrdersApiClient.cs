using software.amzn.spapi.Model.orders.v0;

namespace LSB.SellerCentral.Core.Clients;

/// <summary>
/// Interface for Amazon Seller Partner API Orders operations
/// </summary>
public interface IOrdersApiClient
{
    /// <summary>
    /// Get orders from Amazon SP-API
    /// </summary>
    /// <param name="marketplaceIds">List of marketplace IDs</param>
    /// <param name="createdAfter">Orders created after this date</param>
    /// <param name="createdBefore">Orders created before this date</param>
    /// <param name="lastUpdatedAfter">Orders last updated after this date</param>
    /// <param name="orderStatuses">Filter by order statuses</param>
    /// <param name="maxResultsPerPage">Maximum results per page (default: 100)</param>
    /// <param name="nextToken">Token for pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>GetOrdersResponse containing orders</returns>
    Task<GetOrdersResponse> GetOrdersAsync(
        List<string> marketplaceIds,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        DateTime? lastUpdatedAfter = null,
        List<string>? orderStatuses = null,
        int? maxResultsPerPage = null,
        string? nextToken = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get a specific order by ID
    /// </summary>
    /// <param name="orderId">Amazon Order ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>GetOrderResponse containing order details</returns>
    Task<GetOrderResponse> GetOrderAsync(
        string orderId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get order items for a specific order
    /// </summary>
    /// <param name="orderId">Amazon Order ID</param>
    /// <param name="nextToken">Token for pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>GetOrderItemsResponse containing order items</returns>
    Task<GetOrderItemsResponse> GetOrderItemsAsync(
        string orderId,
        string? nextToken = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Confirm shipment for an order
    /// </summary>
    /// <param name="orderId">Amazon Order ID</param>
    /// <param name="marketplaceId">Marketplace ID</param>
    /// <param name="packageDetail">Package details including tracking information</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful, throws exception on failure</returns>
    Task<bool> ConfirmShipmentAsync(
        string orderId,
        string marketplaceId,
        PackageDetail packageDetail,
        CancellationToken cancellationToken = default);
}
