import React, { useState } from 'react';

interface SyncButtonProps {
  onSync: (orderIds?: string[]) => Promise<void>;
  syncing: boolean;
}

export const SyncButton: React.FC<SyncButtonProps> = ({ onSync, syncing }) => {
  const [showOptions, setShowOptions] = useState(false);

  const handleSyncRecent = () => {
    onSync();
    setShowOptions(false);
  };

  const handleSyncAll = () => {
    // Sync orders from the last 90 days
    onSync();
    setShowOptions(false);
  };

  return (
    <div className="sync-button-container">
      <button
        className={`sync-button ${syncing ? 'syncing' : ''}`}
        onClick={handleSyncRecent}
        disabled={syncing}
        title="Sync recent orders from Amazon"
      >
        {syncing ? (
          <>
            <span className="sync-spinner">⏳</span>
            Syncing...
          </>
        ) : (
          <>
            <span className="sync-icon">🔄</span>
            Sync Orders
          </>
        )}
      </button>

      {!syncing && (
        <button
          className="sync-options-button"
          onClick={() => setShowOptions(!showOptions)}
          title="Sync options"
        >
          ▼
        </button>
      )}

      {showOptions && !syncing && (
        <div className="sync-options-dropdown">
          <button
            className="sync-option"
            onClick={handleSyncRecent}
          >
            <span className="option-icon">📅</span>
            Sync Last 30 Days
          </button>
          <button
            className="sync-option"
            onClick={handleSyncAll}
          >
            <span className="option-icon">📊</span>
            Sync Last 90 Days
          </button>
        </div>
      )}

      {showOptions && (
        <div 
          className="sync-options-overlay"
          onClick={() => setShowOptions(false)}
        />
      )}
    </div>
  );
};

export default SyncButton;
