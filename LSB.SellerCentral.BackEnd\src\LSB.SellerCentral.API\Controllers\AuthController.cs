using Microsoft.AspNetCore.Mvc;
using LSB.SellerCentral.Application.Users.Commands.RegisterUser;
using LSB.SellerCentral.Application.Users.Commands.LoginUser;
using MediatR;

namespace LSB.SellerCentral.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IMediator _mediator;

    public AuthController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost("register")]
    public async Task<IActionResult> Register([FromBody] RegisterUserCommand command)
    {
        var result = await _mediator.Send(command);

        if (!result.Success)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new
        {
            success = true,
            token = result.Token,
            user = new
            {
                email = result.Email,
                firstName = result.FirstName,
                lastName = result.LastName
            }
        });
    }

    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginUserCommand command)
    {
        var result = await _mediator.Send(command);

        if (!result.Success)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new
        {
            success = true,
            token = result.Token,
            user = new
            {
                email = result.Email,
                firstName = result.FirstName,
                lastName = result.LastName
            }
        });
    }
}
