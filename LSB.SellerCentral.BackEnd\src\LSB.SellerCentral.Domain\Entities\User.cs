using LSB.SellerCentral.Domain.Common;

namespace LSB.SellerCentral.Domain.Entities;

public class User : BaseEntity
{
    public string Email { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime? LastLoginDate { get; set; }
    
    // Navigation properties
    public virtual ICollection<Seller> Sellers { get; set; } = new List<Seller>();
}
