### **LSB Seller Central: Detailed Technical Project Plan**

**Project:** LSB Seller Central
**Prepared for:** Tech Lead & Project Manager
**Prepared by:** Gemini, Solutions Architect & Technical Project Manager
**Date:** July 23, 2025

This document outlines the strategic technical plan for the development and launch of the LSB Seller Central Minimum Viable Product (MVP). The plan is designed to balance a rapid time-to-market with the foundational requirements for long-term stability and scalability.

---

### **1. Overall Architecture & Technology Recommendations**

The proposed architecture is designed for scalability, security, and maintainability, separating concerns to allow for independent development and deployment of each component.

#### **System Architecture Diagram**

```
[Amazon Seller's Machine]                                  [Cloud Platform - Azure/AWS]
+-------------------------+                               +-----------------------------------------------------+
|                         |                               |                                                     |
|  [Windows Service]      |---(HTTPS - Pushes Data)------>|  [Central Cloud Database]                             |
|  (Fetches from SP-API)  |                               |  (e.g., Azure SQL, PostgreSQL)                      |
|                         |                               |       ^                                             |
+-------------------------+                               |       | (Reads/Writes)                              |
                                                          |       v                                             |
                                                          |  [Backend API]                                      |
                                                          |  (ASP.NET 8 on App Service/Elastic Beanstalk)       |
                                                          |       ^                                             |
                                                          |       | (HTTPS - API Calls)                         |
[End User - Amazon Seller]                                |       v                                             |
+-------------------------+                               |  [React JS Frontend]                                |
|                         |                               |  (Hosted on Static Web App Service/S3+CloudFront)   |
|  [Web Browser]          |<--(HTTPS - Serves App)---------|                                                     |
|  (Accesses React App)   |                               |                                                     |
+-------------------------+                               +-----------------------------------------------------+

```

#### **Technology Recommendations**

**Central Cloud Database**

*   **Option 1: Azure SQL Database (Serverless Tier)**
    *   **Pros:**
        *   **Excellent Integration:** Offers seamless integration with the .NET ecosystem and Azure App Service.
        *   **Managed Service:** Fully managed by Microsoft, handling backups, patching, and security, reducing DevOps overhead.
        *   **Scalability:** The serverless tier automatically scales compute based on workload demand and can be configured to pause during inactivity, optimizing costs for an MVP with variable load.
    *   **Cons:**
        *   **Cost:** Can be more expensive at higher performance tiers compared to IaaS solutions.
        *   **Vendor Lock-in:** Deeper integration with the Azure ecosystem might make future migrations to other cloud providers more complex.

*   **Option 2: PostgreSQL on Azure/AWS (Managed Service)**
    *   **Pros:**
        *   **Open Source:** No licensing costs and a large, active community for support.
        *   **Flexibility:** Known for its robustness, extensibility, and strong support for complex queries, which will be useful for the "Reports & Payments" feature.
        *   **Cloud Agnostic:** As a standard, it's easier to migrate between cloud providers (AWS RDS for PostgreSQL, Azure Database for PostgreSQL).
    *   **Cons:**
        *   **Slightly More Management:** While a managed service, it may require more fine-tuning for performance compared to the highly integrated Azure SQL.
        *   **Integration:** Integration with .NET is excellent, but not as "native" as SQL Server with the Microsoft stack.

**Authentication & Authorization**

*   **Recommendation: JSON Web Tokens (JWT) with .NET 8 Identity**
    *   **Rationale:** This approach provides a robust, scalable, and cost-effective solution built directly into the ASP.NET framework.
    *   **.NET 8 Identity:** Handles user registration, password hashing, and role management securely out-of-the-box. It's highly customizable for specific needs.
    *   **JWT:** Provides a stateless authentication mechanism, which is ideal for scalable web APIs. The backend API will issue a token upon successful login, which the React frontend will include in subsequent requests. This avoids the need for session state on the server, simplifying scalability.
    *   **Alternative (Auth0):** While excellent, Auth0 introduces an external dependency and a separate cost structure. For the MVP, the built-in capabilities of .NET Identity are sufficient and keep the architecture simpler. Auth0 can be integrated later if more complex identity federation features are required.

**Hosting Platform**

*   **Recommendation: Azure App Service & Azure Static Web Apps**
    *   **Backend API (ASP.NET):** Azure App Service is the ideal platform for hosting .NET applications. It offers built-in CI/CD, auto-scaling, deployment slots (for blue-green deployments), and seamless integration with Azure SQL and other Azure services.
    *   **Frontend (React JS):** Azure Static Web Apps is a service specifically designed for modern web applications. It streamlines the deployment of the React frontend and provides a globally distributed CDN for fast load times. It also has a streamlined workflow for authenticating against a backend API hosted on Azure.
    *   **Alternative (AWS):** AWS Elastic Beanstalk for the backend and AWS S3 (for hosting) + CloudFront (as a CDN) for the frontend is a powerful and popular combination. It offers similar scalability and reliability but may require more configuration effort compared to the highly integrated Azure solution for a .NET stack.

---

### **2. Phased Project Roadmap**

This roadmap prioritizes delivering core value to users quickly while building on a solid technical foundation.

#### **Phase 0: Foundation & Setup (Sprint 0 - 2 Weeks)**

*   **DevOps:**
    *   Set up Git repositories (e.g., in GitHub or Azure Repos) for the Frontend, Backend, and Windows Service.
    *   Establish a basic CI/CD pipeline using GitHub Actions or Azure Pipelines. The initial pipeline will build the code, run tests, and prepare artifacts for deployment.
*   **Backend (LSB.SellerCentral):**
    *   Bootstrap the ASP.NET Web API solution using the Clean Architecture template.
    *   Establish a database project or use Entity Framework Core migrations to manage the schema.
    *   Configure .NET Identity for user management.
    *   Implement the initial database schema for Users (with login credentials) and Sellers (to store Amazon account details).
*   **Frontend:**
    *   Bootstrap the React project using Vite for a fast development experience.
    *   Set up a standard folder structure (e.g., components, pages, services, store).
    *   Implement basic routing (e.g., using React Router).
    *   Integrate and configure a state management library (Redux Toolkit is recommended for its scalability and boilerplate reduction).
    *   Build the user registration and login forms.
*   **Goal:** A developer can pull the code, a new user can register and log in, and the CI/CD pipeline is functional.

#### **Phase 1: MVP - Inventory Management (3 Weeks)**

*   **Windows Service:**
    *   Implement secure storage for the seller's SP-API refresh tokens.
    *   Develop the core logic to call the Amazon SP-API `getInventorySummaries` operation.
    *   Implement data transformation logic to map the SP-API response to the central database schema.
    *   Create the mechanism to reliably push processed inventory data to the central database via a secure API endpoint.
*   **Backend:**
    *   Create the database schema for Products/Inventory.
    *   Build secure API endpoints for inventory data (e.g., `GET /api/inventory`, `PUT /api/inventory/{sku}`). The PUT endpoint will allow for future manual stock adjustments.
    *   Implement authorization policies to ensure a user can only access their own seller data.
*   **Frontend:**
    *   Develop the main Inventory page.
    *   Implement a data grid component (e.g., using AG Grid or TanStack Table) to display product and inventory data.
    *   Add features for client-side or server-side searching and filtering of the inventory list.
    *   Implement inline editing functionality for key fields like stock levels, triggering calls to the `PUT /api/inventory/{sku}` endpoint.

#### **Phase 2: MVP - Dashboard (2 Weeks)**

*   **Backend:**
    *   Design and create optimized database views or summary tables to pre-aggregate key metrics (e.g., total inventory value, units sold today, pending orders). This avoids performance-heavy queries on the raw data tables.
    *   Build performant API endpoints for the dashboard (e.g., `GET /api/dashboard/summary`, `GET /api/dashboard/sales-over-time`).
*   **Frontend:**
    *   Build the Dashboard page UI.
    *   Use a charting library (e.g., Recharts or Chart.js) to create visualizations for sales trends, inventory status, etc.
    *   Develop summary card components to display key metrics fetched from the dashboard API endpoints.

#### **Phase 3: MVP - Order Management (3 Weeks)**

*   **Windows Service:**
    *   Extend the service to call the SP-API `getOrders` and `getOrder` operations.
    *   Implement logic to handle fetching only new or updated orders since the last check to minimize API usage.
    *   Push new and updated order data to the central database.
*   **Backend:**
    *   Create the database schema for Orders and OrderItems.
    *   Build API endpoints to manage orders (e.g., `GET /api/orders`, `GET /api/orders?status=shipped`, `GET /api/orders/{id}`).
    *   Implement robust filtering and pagination for order queries.
*   **Frontend:**
    *   Develop the Orders page UI.
    *   Implement a searchable and filterable list/table of customer orders.
    *   Create a detail view/modal to show all information for a specific order.

---

### **3. Risk Management**

Proactively identifying and planning for risks is crucial for project success.

*   **Risk 1: Amazon SP-API Rate Limiting & Throttling**
    *   **Description:** The Amazon Selling Partner API enforces strict rate limits. The Windows Service, especially if polling frequently or for sellers with large datasets, could exceed these limits, leading to temporary service disruption.
    *   **Mitigation Strategy:**
        1.  **Queueing & Caching:** Implement a local queue (e.g., using a lightweight local DB like SQLite) on the Windows Service to manage API requests.
        2.  **Adaptive Polling:** The service should use a "leaky bucket" or "token bucket" algorithm to stay within the defined rate limits.
        3.  **Error Handling:** Implement exponential backoff with jitter for handling `429 (Too Many Requests)` error responses gracefully.
        4.  **Targeted Data Fetching:** Request only the data needed and use cursors/tokens provided by the SP-API for pagination instead of re-fetching entire datasets.

*   **Risk 2: Data Synchronization & Integrity Failures**
    *   **Description:** A network failure, database error, or bug during the data push from the Windows Service to the central database could result in stale or incomplete data in the web application.
    *   **Mitigation Strategy:**
        1.  **Transactional Pushes:** When pushing data, use a single transaction on the central database. If any part of the data update fails, the entire transaction is rolled back.
        2.  **Idempotent Operations:** Design the API endpoint that receives data from the service to be idempotent. This means that if the service sends the same batch of data twice (e.g., after a timeout and retry), it doesn't create duplicate records.
        3.  **Last Sync Timestamp:** The service should record the timestamp of the last successful synchronization. In case of failure, it can resume from that point, preventing data gaps.
        4.  **Health Checks:** Implement a health check endpoint that the service can ping. The service should pause data pushes if the central API is unhealthy.

*   **Risk 3: Security of Seller SP-API Credentials**
    *   **Description:** The Windows Service installed on a seller's machine will need to store and manage the SP-API refresh token. If this machine is compromised, the token could be stolen, granting unauthorized access to the seller's Amazon account.
    *   **Mitigation Strategy:**
        1.  **Secure Storage:** Do not store the refresh token in a plain text file. Use the Windows Data Protection API (DPAPI), which encrypts the data using keys tied to the user's or machine's credentials. This makes it difficult for another user or process to access the token.
        2.  **Limited Scope:** When initiating the OAuth flow, request only the minimum necessary permissions from the seller for the MVP's functionality.
        3.  **Backend Token Validation:** The central backend should never receive or store the seller's SP-API token. Its interaction is solely with the central database, which is populated by the service.
        4.  **Clear Security Guidance:** Provide clear documentation to sellers on the importance of securing the machine where the service is installed.

---

### **4. Roles & Responsibilities Proposal**

**Team:**
*   **You:** PM / Tech Lead / Backend Developer
*   **2x** Frontend Developers

This structure allows you to focus on the overall architecture, backend development, and project oversight while empowering the frontend team to take ownership of the user interface.

*   **You (PM / Tech Lead / Backend Dev):**
    *   **Project Management:** Define sprint goals, manage the backlog, and track progress against the roadmap. Act as the primary point of contact for project status.
    *   **Technical Leadership:** Make final decisions on architecture, technology choices, and development standards. Oversee code reviews for all parts of the system (Frontend, Backend, Service).
    *   **Backend Development:** Take full ownership of designing, building, and testing the ASP.NET Web API and the database schema.
    *   **Windows Service Development:** Lead the development of the Windows Service, as its logic is tightly coupled with the backend API and database structure.
    *   **DevOps:** Set up and maintain the CI/CD pipelines and hosting environments.

*   **Frontend Developer 1 (Lead Frontend):**
    *   **Responsibilities:**
        *   Lead the development of the React application's core structure, including routing, state management, and component architecture.
        *   Define frontend coding standards and best practices.
        *   Implement the more complex UI features (e.g., the initial data grid for Inventory Management, charting for the Dashboard).
        *   Collaborate closely with you on API contract design (`/api/...`).
        *   Mentor Frontend Developer 2.

*   **Frontend Developer 2:**
    *   **Responsibilities:**
        *   Develop specific UI components based on the established architecture (e.g., login/registration forms, order list items, dashboard summary cards).
        *   Assist with implementing the UI for major features (e.g., the order detail view).
        *   Focus on CSS styling, responsiveness, and ensuring a consistent user experience across the application.
        *   Write unit and integration tests for React components.

This division of labor creates clear ownership, fosters collaboration on API design, and ensures that all aspects of the project receive dedicated attention.