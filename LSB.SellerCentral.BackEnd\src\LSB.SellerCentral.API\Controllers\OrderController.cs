using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using LSB.SellerCentral.Application.Orders.DTOs;
using LSB.SellerCentral.Application.Orders.Commands;
using LSB.SellerCentral.Application.Orders.Queries;
using MediatR;

namespace LSB.SellerCentral.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class OrderController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<OrderController> _logger;

    public OrderController(
        IMediator mediator,
        ILogger<OrderController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get orders with filtering and pagination
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PaginatedOrdersDto>> GetOrders([FromQuery] OrderFilterDto filter)
    {
        try
        {
            var query = new GetOrdersQuery
            {
                StartDate = filter.StartDate,
                EndDate = filter.EndDate,
                OrderStatus = filter.OrderStatus,
                FulfillmentChannel = filter.FulfillmentChannel,
                SearchTerm = filter.SearchTerm,
                HasTracking = filter.HasTracking,
                Page = filter.Page,
                PageSize = filter.PageSize
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving orders");
            return StatusCode(500, new { error = "An error occurred while retrieving orders" });
        }
    }

    /// <summary>
    /// Get a specific order by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<OrderDto>> GetOrder(int id, [FromQuery] bool refresh = false)
    {
        try
        {
            var query = new GetOrderByIdQuery
            {
                OrderId = id,
                RefreshFromSpApi = refresh
            };

            var orderDto = await _mediator.Send(query);

            if (orderDto == null)
                return NotFound(new { error = "Order not found" });

            return Ok(orderDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving order {OrderId}", id);
            return StatusCode(500, new { error = "An error occurred while retrieving the order" });
        }
    }

    /// <summary>
    /// Update tracking number for an order
    /// </summary>
    [HttpPut("{id}/tracking")]
    public async Task<ActionResult> UpdateTracking(int id, [FromBody] UpdateTrackingDto trackingDto)
    {
        try
        {
            var command = new UpdateTrackingCommand
            {
                OrderId = id,
                TrackingNumber = trackingDto.TrackingNumber,
                CarrierName = trackingDto.CarrierName,
                ShipmentDate = trackingDto.ShipmentDate
            };

            var success = await _mediator.Send(command);

            if (!success)
                return NotFound(new { error = "Order not found" });

            return Ok(new { message = "Tracking information updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tracking for order {OrderId}", id);
            return StatusCode(500, new { error = "An error occurred while updating tracking information" });
        }
    }

    /// <summary>
    /// Sync orders from SP-API
    /// </summary>
    [HttpPost("sync")]
    public async Task<ActionResult> SyncOrders([FromBody] SyncOrdersDto? syncDto = null)
    {
        try
        {
            var command = new SyncOrdersCommand
            {
                OrderIds = syncDto?.OrderIds,
                SyncFromDate = syncDto?.SyncFromDate
            };

            var result = await _mediator.Send(command);

            if (result.Success)
            {
                return Ok(new {
                    message = result.Message,
                    ordersSynced = result.OrdersSynced,
                    errors = result.Errors
                });
            }
            else
            {
                return StatusCode(500, new {
                    error = result.Message,
                    errors = result.Errors
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing orders from SP-API");
            return StatusCode(500, new { error = "An error occurred while syncing orders" });
        }
    }
}
