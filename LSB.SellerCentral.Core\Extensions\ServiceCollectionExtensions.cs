using LSB.SellerCentral.Core.Authentication;
using LSB.SellerCentral.Core.Clients;
using LSB.SellerCentral.Core.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace LSB.SellerCentral.Core.Extensions;

/// <summary>
/// Extension methods for IServiceCollection to register SP-API services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds SP-API services to the service collection
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration instance</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddSpApiServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Register configuration
        services.Configure<SpApiConfiguration>(options =>
            configuration.GetSection(SpApiConfiguration.SectionName).Bind(options));

        // Validate configuration on startup
        services.AddSingleton<IValidateOptions<SpApiConfiguration>, SpApiConfigurationValidator>();

        // Register authentication service
        services.AddSingleton<ISpApiAuthenticationService, SpApiAuthenticationService>();

        // Register API clients
        services.AddScoped<ISellersApiClient, SellersApiClient>();
        services.AddScoped<IOrdersApiClient, OrdersApiClient>();

        return services;
    }

    /// <summary>
    /// Adds SP-API services to the service collection with custom configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configureOptions">Action to configure SP-API options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddSpApiServices(
        this IServiceCollection services,
        Action<SpApiConfiguration> configureOptions)
    {
        // Register configuration
        services.Configure(configureOptions);

        // Validate configuration on startup
        services.AddSingleton<IValidateOptions<SpApiConfiguration>, SpApiConfigurationValidator>();

        // Register authentication service
        services.AddSingleton<ISpApiAuthenticationService, SpApiAuthenticationService>();

        // Register API clients
        services.AddScoped<SellersApiClient>();

        return services;
    }
}

/// <summary>
/// Validator for SP-API configuration
/// </summary>
internal class SpApiConfigurationValidator : IValidateOptions<SpApiConfiguration>
{
    public ValidateOptionsResult Validate(string? name, SpApiConfiguration options)
    {
        if (options == null)
        {
            return ValidateOptionsResult.Fail("SpApiConfiguration cannot be null");
        }

        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(options.ClientId))
        {
            errors.Add("ClientId is required");
        }

        if (string.IsNullOrWhiteSpace(options.ClientSecret))
        {
            errors.Add("ClientSecret is required");
        }

        if (string.IsNullOrWhiteSpace(options.RefreshToken))
        {
            errors.Add("RefreshToken is required");
        }

        if (string.IsNullOrWhiteSpace(options.ApiEndpoint))
        {
            errors.Add("ApiEndpoint is required");
        }

        if (string.IsNullOrWhiteSpace(options.LwaEndpoint))
        {
            errors.Add("LwaEndpoint is required");
        }

        if (string.IsNullOrWhiteSpace(options.AwsRegion))
        {
            errors.Add("AwsRegion is required");
        }

        if (options.MarketplaceIds == null || options.MarketplaceIds.Count == 0)
        {
            errors.Add("At least one MarketplaceId is required");
        }

        if (options.RequestTimeoutSeconds <= 0)
        {
            errors.Add("RequestTimeoutSeconds must be greater than 0");
        }

        if (options.MaxRetryAttempts < 0)
        {
            errors.Add("MaxRetryAttempts must be greater than or equal to 0");
        }

        if (options.RetryDelayMs <= 0)
        {
            errors.Add("RetryDelayMs must be greater than 0");
        }

        if (errors.Count > 0)
        {
            return ValidateOptionsResult.Fail(errors);
        }

        return ValidateOptionsResult.Success;
    }
}
