# PowerShell script to install LSB Seller Central Data Collector as Windows Service
# Run this script as Administrator

param(
    [string]$ServicePath = "",
    [string]$ServiceName = "LSBSellerCentralDataCollector",
    [string]$DisplayName = "LSB Seller Central Data Collector",
    [string]$Description = "Automated data collection service for LSB Seller Central from Amazon SP-API"
)

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script must be run as Administrator. Please run PowerShell as Administrator and try again."
    exit 1
}

# Set default service path if not provided
if ([string]::IsNullOrEmpty($ServicePath)) {
    $ServicePath = Join-Path $PSScriptRoot "..\bin\Release\net9.0\LSB.SellerCentral.Services.exe"
}

# Check if service executable exists
if (-not (Test-Path $ServicePath)) {
    Write-Error "Service executable not found at: $ServicePath"
    Write-Host "Please build the project in Release mode first:"
    Write-Host "dotnet publish -c Release"
    exit 1
}

try {
    # Check if service already exists
    $existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    
    if ($existingService) {
        Write-Host "Service '$ServiceName' already exists. Stopping and removing..."
        
        # Stop the service if it's running
        if ($existingService.Status -eq 'Running') {
            Stop-Service -Name $ServiceName -Force
            Write-Host "Service stopped."
        }
        
        # Remove the existing service
        sc.exe delete $ServiceName
        Write-Host "Existing service removed."
        
        # Wait a moment for the service to be fully removed
        Start-Sleep -Seconds 2
    }
    
    # Install the new service
    Write-Host "Installing service '$ServiceName'..."
    
    $result = sc.exe create $ServiceName binPath= "`"$ServicePath`"" start= auto DisplayName= "$DisplayName"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Service installed successfully."
        
        # Set service description
        sc.exe description $ServiceName "$Description"
        
        # Configure service recovery options
        sc.exe failure $ServiceName reset= 86400 actions= restart/60000/restart/60000/restart/60000
        
        Write-Host "Service configuration completed."
        Write-Host ""
        Write-Host "To start the service, run:"
        Write-Host "Start-Service -Name $ServiceName"
        Write-Host ""
        Write-Host "To check service status, run:"
        Write-Host "Get-Service -Name $ServiceName"
        
    } else {
        Write-Error "Failed to install service. Exit code: $LASTEXITCODE"
        exit 1
    }
    
} catch {
    Write-Error "An error occurred: $($_.Exception.Message)"
    exit 1
}

Write-Host "Service installation completed successfully!"
