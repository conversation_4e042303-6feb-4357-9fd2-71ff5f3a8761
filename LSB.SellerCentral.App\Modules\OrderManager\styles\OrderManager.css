/* Order Manager Styles */

.order-manager {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.order-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.order-manager-header h1 {
  margin: 0;
  color: #333;
  font-size: 28px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* Error Message */
.error-message {
  background-color: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message button {
  background: none;
  border: none;
  color: #c33;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
}

/* Order Stats */
.order-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  font-weight: 500;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* Order Filters */
.order-filters {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-bottom: 20px;
  overflow: hidden;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.search-section {
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  gap: 10px;
}

.expand-filters-button,
.clear-filters-button {
  padding: 8px 16px;
  border: 1px solid #007bff;
  background-color: #fff;
  color: #007bff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.expand-filters-button:hover,
.clear-filters-button:hover {
  background-color: #007bff;
  color: #fff;
}

.clear-filters-button {
  border-color: #dc3545;
  color: #dc3545;
}

.clear-filters-button:hover {
  background-color: #dc3545;
}

.filters-expanded {
  padding: 20px;
  background-color: #fff;
}

.filter-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
  min-width: 150px;
}

.filter-group label {
  font-size: 12px;
  font-weight: 500;
  color: #555;
  text-transform: uppercase;
}

.date-input,
.select-input {
  padding: 6px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.quick-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-filter-button {
  padding: 4px 8px;
  border: 1px solid #6c757d;
  background-color: #fff;
  color: #6c757d;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.quick-filter-button:hover {
  background-color: #6c757d;
  color: #fff;
}

.active-filters {
  padding: 10px 15px;
  background-color: #e7f3ff;
  border-top: 1px solid #b3d9ff;
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.active-filters-label {
  font-size: 12px;
  font-weight: 500;
  color: #0066cc;
}

.active-filter-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-tag {
  background-color: #007bff;
  color: #fff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.filter-tag button {
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  line-height: 1;
}

/* Sync Button */
.sync-button-container {
  position: relative;
  display: flex;
}

.sync-button {
  padding: 8px 16px;
  border: 1px solid #28a745;
  background-color: #28a745;
  color: #fff;
  border-radius: 4px 0 0 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.sync-button:hover:not(:disabled) {
  background-color: #218838;
}

.sync-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.sync-options-button {
  padding: 8px 8px;
  border: 1px solid #28a745;
  border-left: none;
  background-color: #28a745;
  color: #fff;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-size: 12px;
}

.sync-options-button:hover {
  background-color: #218838;
}

.sync-options-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  z-index: 1000;
  min-width: 200px;
}

.sync-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 10px 15px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
}

.sync-option:hover {
  background-color: #f8f9fa;
}

.sync-options-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.sync-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Order Table */
.order-table-container {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.order-table-loading,
.order-table-empty {
  padding: 40px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.order-table-wrapper {
  overflow-x: auto;
}

.order-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.order-table th {
  background-color: #f8f9fa;
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  white-space: nowrap;
}

.order-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: top;
}

.order-row:hover {
  background-color: #f8f9fa;
}

.expand-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: #666;
  font-size: 12px;
}

.order-id {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.order-id strong {
  font-size: 13px;
}

.order-id small {
  font-size: 11px;
  color: #666;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-pending { background-color: #fff3cd; color: #856404; }
.status-unshipped { background-color: #f8d7da; color: #721c24; }
.status-partial { background-color: #d1ecf1; color: #0c5460; }
.status-shipped { background-color: #d4edda; color: #155724; }
.status-canceled { background-color: #f5c6cb; color: #721c24; }
.status-default { background-color: #e2e3e5; color: #383d41; }

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.customer-email {
  font-size: 12px;
  color: #666;
}

.business-badge {
  background-color: #007bff;
  color: #fff;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  align-self: flex-start;
}

.fulfillment-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.fulfillment-badge.mfn {
  background-color: #e7f3ff;
  color: #0066cc;
}

.fulfillment-badge.afn {
  background-color: #fff3e0;
  color: #e65100;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.refresh-button {
  background: none;
  border: 1px solid #6c757d;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.refresh-button:hover {
  background-color: #6c757d;
  color: #fff;
}

/* Order Details */
.order-details-row td {
  background-color: #f8f9fa;
  border-top: none;
}

.order-details {
  padding: 15px;
}

.order-details-section {
  margin-bottom: 20px;
}

.order-details-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-item label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
}

.detail-item span {
  font-size: 13px;
  color: #333;
}

.order-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.order-item {
  padding: 10px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.item-info strong {
  display: block;
  margin-bottom: 5px;
  color: #333;
}

.item-details {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #666;
  flex-wrap: wrap;
}

/* Tracking Input */
.tracking-display {
  cursor: pointer;
  min-height: 20px;
  display: flex;
  align-items: center;
}

.tracking-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tracking-number {
  font-weight: 500;
  color: #007bff;
  font-size: 12px;
}

.carrier-name {
  font-size: 11px;
  color: #666;
}

.no-tracking {
  color: #999;
  font-style: italic;
  font-size: 12px;
}

.add-tracking-text {
  color: #007bff;
}

.tracking-input-container {
  min-width: 200px;
}

.tracking-input-form {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.tracking-input {
  padding: 4px 6px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-size: 12px;
  width: 100%;
}

.tracking-input.error {
  border-color: #dc3545;
}

.carrier-select {
  padding: 4px 6px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-size: 12px;
  width: 100%;
}

.tracking-input-actions {
  display: flex;
  gap: 5px;
}

.save-button,
.cancel-button {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  flex: 1;
}

.save-button {
  background-color: #28a745;
  color: #fff;
}

.save-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.cancel-button {
  background-color: #6c757d;
  color: #fff;
}

.tracking-input-error {
  color: #dc3545;
  font-size: 11px;
  margin-top: 2px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  flex-wrap: wrap;
  gap: 10px;
}

.pagination-info {
  font-size: 14px;
  color: #666;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-button {
  padding: 6px 10px;
  border: 1px solid #dee2e6;
  background-color: #fff;
  color: #007bff;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
}

.pagination-button:hover:not(:disabled) {
  background-color: #e9ecef;
}

.pagination-button:disabled {
  color: #6c757d;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 2px;
}

.pagination-page {
  padding: 6px 10px;
  border: 1px solid #dee2e6;
  background-color: #fff;
  color: #007bff;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
  min-width: 35px;
  text-align: center;
}

.pagination-page:hover {
  background-color: #e9ecef;
}

.pagination-page.active {
  background-color: #007bff;
  color: #fff;
  border-color: #007bff;
}

.pagination-dots {
  padding: 6px 4px;
  color: #6c757d;
  font-size: 14px;
}

.pagination-jump {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #666;
}

.pagination-jump-input {
  width: 60px;
  padding: 4px 6px;
  border: 1px solid #ccc;
  border-radius: 3px;
  text-align: center;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .order-manager {
    padding: 10px;
  }
  
  .order-manager-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .order-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .filters-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .filter-row {
    flex-direction: column;
    gap: 10px;
  }
  
  .pagination {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .pagination-controls {
    justify-content: center;
  }
}
