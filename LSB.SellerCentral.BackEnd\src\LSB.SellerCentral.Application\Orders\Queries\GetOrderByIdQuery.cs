using LSB.SellerCentral.Application.Common.Interfaces;
using LSB.SellerCentral.Application.Orders.DTOs;
using LSB.SellerCentral.Application.Orders.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LSB.SellerCentral.Application.Orders.Queries;

public class GetOrderByIdQuery : IRequest<OrderDto?>
{
    public int OrderId { get; set; }
    public bool RefreshFromSpApi { get; set; } = false;
}

public class GetOrderByIdQueryHandler : IRequestHandler<GetOrderByIdQuery, OrderDto?>
{
    private readonly IApplicationDbContext _context;
    private readonly IOrderSyncService _orderSyncService;
    private readonly ILogger<GetOrderByIdQueryHandler> _logger;

    public GetOrderByIdQueryHandler(
        IApplicationDbContext context,
        IOrderSyncService orderSyncService,
        ILogger<GetOrderByIdQueryHandler> logger)
    {
        _context = context;
        _orderSyncService = orderSyncService;
        _logger = logger;
    }

    public async Task<OrderDto?> Handle(GetOrderByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var order = await _context.Orders
                .Include(o => o.Seller)
                .Include(o => o.OrderItems)
                .FirstOrDefaultAsync(o => o.Id == request.OrderId, cancellationToken);

            if (order == null)
            {
                _logger.LogWarning("Order with ID {OrderId} not found", request.OrderId);
                return null;
            }

            // If refresh is requested, sync from SP-API
            if (request.RefreshFromSpApi)
            {
                try
                {
                    await _orderSyncService.SyncOrderFromSpApiAsync(order.AmazonOrderId, cancellationToken);
                    
                    // Reload the order after sync
                    order = await _context.Orders
                        .Include(o => o.Seller)
                        .Include(o => o.OrderItems)
                        .FirstOrDefaultAsync(o => o.Id == request.OrderId, cancellationToken);

                    _logger.LogInformation("Refreshed order {OrderId} from SP-API", request.OrderId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to refresh order {OrderId} from SP-API", request.OrderId);
                    // Continue with existing data
                }
            }

            if (order == null)
            {
                return null;
            }

            var orderDto = new OrderDto
            {
                Id = order.Id,
                AmazonOrderId = order.AmazonOrderId,
                OrderStatus = order.OrderStatus,
                PurchaseDate = order.PurchaseDate,
                LastUpdateDate = order.LastUpdateDate,
                CustomerEmail = order.CustomerEmail,
                ShippingAddress = order.ShippingAddress,
                TotalAmount = order.TotalAmount,
                Currency = order.Currency,
                FulfillmentChannel = order.FulfillmentChannel,
                ShipServiceLevel = order.ShipServiceLevel,
                IsBusinessOrder = order.IsBusinessOrder,
                EarliestShipDate = order.EarliestShipDate,
                LatestShipDate = order.LatestShipDate,
                EarliestDeliveryDate = order.EarliestDeliveryDate,
                LatestDeliveryDate = order.LatestDeliveryDate,
                TrackingNumber = order.TrackingNumber,
                CarrierName = order.CarrierName,
                ShipmentDate = order.ShipmentDate,
                LastSyncDate = order.LastSyncDate,
                SellerId = order.SellerId,
                SellerName = order.Seller.SellerCentralBusinessName,
                OrderItems = order.OrderItems.Select(oi => new OrderItemDto
                {
                    Id = oi.Id,
                    Sku = oi.Sku,
                    Asin = oi.Asin,
                    ProductName = oi.ProductName,
                    Quantity = oi.Quantity,
                    UnitPrice = oi.UnitPrice,
                    TotalPrice = oi.TotalPrice,
                    Currency = oi.Currency,
                    PromotionDiscount = oi.PromotionDiscount,
                    GiftWrapType = oi.GiftWrapType,
                    GiftMessageText = oi.GiftMessageText
                }).ToList()
            };

            _logger.LogInformation("Retrieved order {OrderId} ({AmazonOrderId})", request.OrderId, order.AmazonOrderId);
            return orderDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve order {OrderId}", request.OrderId);
            throw;
        }
    }
}
