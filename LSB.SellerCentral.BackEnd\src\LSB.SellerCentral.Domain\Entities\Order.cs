using LSB.SellerCentral.Domain.Common;

namespace LSB.SellerCentral.Domain.Entities;

public class Order : BaseEntity
{
    public int SellerId { get; set; }
    public string AmazonOrderId { get; set; } = string.Empty;
    public string OrderStatus { get; set; } = string.Empty;
    public DateTime PurchaseDate { get; set; }
    public DateTime? LastUpdateDate { get; set; }
    public string? CustomerEmail { get; set; }
    public string? ShippingAddress { get; set; }
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = "USD";
    public string? FulfillmentChannel { get; set; }
    public string? ShipServiceLevel { get; set; }
    public bool IsBusinessOrder { get; set; } = false;
    public DateTime? EarliestShipDate { get; set; }
    public DateTime? LatestShipDate { get; set; }
    public DateTime? EarliestDeliveryDate { get; set; }
    public DateTime? LatestDeliveryDate { get; set; }
    
    // Navigation properties
    public virtual Seller Seller { get; set; } = null!;
    public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();
}
