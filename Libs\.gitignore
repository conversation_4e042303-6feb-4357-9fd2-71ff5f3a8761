# LSB Seller Central Libs - Third-party Libraries .gitignore

# Compiled libraries
*.dll
*.so
*.dylib
*.a
*.lib

# Package files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# NuGet packages
*.nupkg
*.snupkg
packages/
!packages/repositories.config

# Node modules
node_modules/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Java
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# C/C++
*.o
*.obj
*.exe
*.out
*.app

# Logs
*.log

# Temporary files
*.tmp
*.temp
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Documentation
docs/build/
*.pdf
*.doc
*.docx

# Archives
*.7z
*.dmg
*.gz
*.iso
*.tar
*.zip

# Backup files
*.backup
*.bak
*.orig

# Cache directories
cache/
.cache/

# Local configuration
local.config
*.local

# Test files
test/
tests/
*.test.*

# Build artifacts
build/
dist/
out/

# Version control
.git/
.svn/
.hg/

# Package manager files
package-lock.json
yarn.lock
composer.lock
Pipfile.lock

# Environment files
.env
.env.local
.env.*.local

# Database files
*.db
*.sqlite
*.sqlite3

# Certificates and keys
*.pem
*.key
*.crt
*.p12
*.pfx

# Sensitive data
secrets/
private/
confidential/
