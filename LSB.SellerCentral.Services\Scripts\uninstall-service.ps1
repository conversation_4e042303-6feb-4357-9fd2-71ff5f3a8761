# PowerShell script to uninstall LSB Seller Central Data Collector Windows Service
# Run this script as Administrator

param(
    [string]$ServiceName = "LSBSellerCentralDataCollector"
)

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script must be run as Administrator. Please run PowerShell as Administrator and try again."
    exit 1
}

try {
    # Check if service exists
    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    
    if (-not $service) {
        Write-Host "Service '$ServiceName' does not exist."
        exit 0
    }
    
    Write-Host "Found service '$ServiceName'. Proceeding with uninstallation..."
    
    # Stop the service if it's running
    if ($service.Status -eq 'Running') {
        Write-Host "Stopping service..."
        Stop-Service -Name $ServiceName -Force
        
        # Wait for the service to stop
        $timeout = 30
        $elapsed = 0
        while ((Get-Service -Name $ServiceName).Status -eq 'Running' -and $elapsed -lt $timeout) {
            Start-Sleep -Seconds 1
            $elapsed++
        }
        
        if ((Get-Service -Name $ServiceName).Status -eq 'Running') {
            Write-Warning "Service did not stop within $timeout seconds. Forcing removal..."
        } else {
            Write-Host "Service stopped successfully."
        }
    }
    
    # Remove the service
    Write-Host "Removing service..."
    $result = sc.exe delete $ServiceName
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Service '$ServiceName' has been successfully uninstalled."
    } else {
        Write-Error "Failed to uninstall service. Exit code: $LASTEXITCODE"
        exit 1
    }
    
} catch {
    Write-Error "An error occurred: $($_.Exception.Message)"
    exit 1
}

Write-Host "Service uninstallation completed successfully!"
