using LSB.SellerCentral.Core.Clients;
using LSB.SellerCentral.Core.Configuration;
using Microsoft.Extensions.Options;

namespace LSB.SellerCentral.Services;

/// <summary>
/// Background service for collecting data from Amazon Seller Partner API
/// </summary>
public class DataCollectorWorker : BackgroundService
{
    private readonly ILogger<DataCollectorWorker> _logger;
    private readonly ISellersApiClient _sellersApiClient;
    private readonly SpApiConfiguration _configuration;
    private readonly TimeSpan _collectionInterval;

    public DataCollectorWorker(
        ILogger<DataCollectorWorker> logger,
        ISellersApiClient sellersApiClient,
        IOptions<SpApiConfiguration> configuration)
    {
        _logger = logger;
        _sellersApiClient = sellersApiClient;
        _configuration = configuration.Value;

        // Default collection interval: every 30 minutes
        _collectionInterval = TimeSpan.FromMinutes(30);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("LSB Seller Central Data Collector started at: {time}", DateTimeOffset.Now);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CollectMarketplaceDataAsync(stoppingToken);

                _logger.LogInformation("Data collection completed. Next collection in {interval} minutes.",
                    _collectionInterval.TotalMinutes);

                await Task.Delay(_collectionInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during data collection. Retrying in 5 minutes.");
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
        }

        _logger.LogInformation("LSB Seller Central Data Collector stopped at: {time}", DateTimeOffset.Now);
    }

    private async Task CollectMarketplaceDataAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting marketplace data collection...");

        try
        {
            // Get marketplace participations
            var marketplaceParticipations = await _sellersApiClient.GetMarketplaceParticipationsAsync(cancellationToken);

            _logger.LogInformation("Retrieved {count} marketplace participations",
                marketplaceParticipations?.Payload?.Count ?? 0);

            // TODO: Store the data in database
            // TODO: Add more data collection endpoints (inventory, orders, etc.)

            _logger.LogInformation("Marketplace data collection completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to collect marketplace data");
            throw;
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("LSB Seller Central Data Collector is stopping...");
        await base.StopAsync(cancellationToken);
    }
}
