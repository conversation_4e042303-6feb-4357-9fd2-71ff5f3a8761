using software.amzn.spapi.Model.sellers.v1;

namespace LSB.SellerCentral.Core.Clients;

/// <summary>
/// Interface for Amazon Seller Partner API Sellers operations
/// </summary>
public interface ISellersApiClient
{
    /// <summary>
    /// Gets marketplace participations for the seller
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Marketplace participations response</returns>
    Task<GetMarketplaceParticipationsResponse> GetMarketplaceParticipationsAsync(CancellationToken cancellationToken = default);
}
