# LSB Seller Central Data Collector Service

A Windows Service for automated data collection from Amazon Seller Partner API.

## Overview

This service runs in the background and periodically collects data from Amazon's Seller Partner API, including:
- Marketplace participations
- Inventory data (planned)
- Order data (planned)
- Product catalog data (planned)

## Prerequisites

- .NET 9.0 Runtime
- Windows 10/11 or Windows Server 2016+
- Administrator privileges for service installation
- Valid Amazon Seller Partner API credentials

## Configuration

### 1. SP-API Credentials

Update the `appsettings.json` file with your Amazon SP-API credentials:

```json
{
  "SpApi": {
    "ClientId": "YOUR_CLIENT_ID",
    "ClientSecret": "YOUR_CLIENT_SECRET",
    "RefreshToken": "YOUR_REFRESH_TOKEN",
    "ApiEndpoint": "https://sellingpartnerapi-na.amazon.com",
    "LwaEndpoint": "https://api.amazon.com/auth/o2/token",
    "AwsRegion": "us-east-1",
    "MarketplaceIds": [
      "ATVPDKIKX0DER"
    ]
  }
}
```

### 2. Logging Configuration

The service uses structured logging with configurable levels. Logs are written to:
- Windows Event Log (Application log)
- Console (when running in development mode)

## Installation

### Option 1: Using Deployment Script (Recommended)

1. Open PowerShell as Administrator
2. Navigate to the project directory
3. Run the deployment script:

```powershell
.\Scripts\deploy-service.ps1
```

This will:
- Build the project in Release mode
- Publish the application
- Install the Windows Service
- Start the service

### Option 2: Manual Installation

1. Build and publish the project:
```bash
dotnet publish -c Release --self-contained false --runtime win-x64
```

2. Run the installation script as Administrator:
```powershell
.\Scripts\install-service.ps1 -ServicePath ".\bin\Release\net9.0\publish\LSB.SellerCentral.Services.exe"
```

3. Start the service:
```powershell
Start-Service -Name "LSBSellerCentralDataCollector"
```

## Service Management

### Check Service Status
```powershell
Get-Service -Name "LSBSellerCentralDataCollector"
```

### Start Service
```powershell
Start-Service -Name "LSBSellerCentralDataCollector"
```

### Stop Service
```powershell
Stop-Service -Name "LSBSellerCentralDataCollector"
```

### View Service Logs
```powershell
Get-EventLog -LogName Application -Source "LSBSellerCentralDataCollector" -Newest 10
```

### Uninstall Service
```powershell
.\Scripts\uninstall-service.ps1
```

## Data Collection Schedule

The service collects data every 30 minutes by default. This can be configured by modifying the `_collectionInterval` in the `DataCollectorWorker` class.

## Error Handling

The service includes comprehensive error handling:
- Automatic retry on transient failures
- Exponential backoff for rate limiting
- Detailed logging of all operations
- Service recovery on unexpected failures

## Monitoring

Monitor the service through:
- Windows Event Viewer (Application log)
- Service status in Windows Services console
- Performance counters (if implemented)

## Troubleshooting

### Service Won't Start
1. Check Windows Event Log for error details
2. Verify SP-API credentials are correct
3. Ensure .NET 9.0 runtime is installed
4. Check file permissions on service directory

### Authentication Errors
1. Verify ClientId, ClientSecret, and RefreshToken
2. Check that the SP-API application is approved
3. Ensure marketplace IDs are correct for your region

### Rate Limiting
The service automatically handles rate limiting with exponential backoff. If you see frequent rate limit errors, consider:
- Reducing collection frequency
- Implementing more sophisticated throttling
- Checking for other applications using the same credentials

## Development

### Running in Development Mode
```bash
dotnet run
```

### Building
```bash
dotnet build
```

### Testing
```bash
dotnet test
```

## Architecture

The service is built using:
- .NET 9.0 Worker Service template
- Microsoft.Extensions.Hosting.WindowsServices
- LSB.SellerCentral.Core library for SP-API integration
- Structured logging with Microsoft.Extensions.Logging

## Security Considerations

- Store SP-API credentials securely (consider Azure Key Vault or similar)
- Run service with minimal required privileges
- Regularly rotate refresh tokens
- Monitor for unauthorized API usage