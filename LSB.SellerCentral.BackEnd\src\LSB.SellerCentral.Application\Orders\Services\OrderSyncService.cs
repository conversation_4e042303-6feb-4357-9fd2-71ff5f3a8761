using LSB.SellerCentral.Application.Common.Interfaces;
using LSB.SellerCentral.Core.Clients;
using LSB.SellerCentral.Core.Configuration;
using LSB.SellerCentral.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using software.amzn.spapi.Model.orders.v0;
using DomainOrder = LSB.SellerCentral.Domain.Entities.Order;
using SpApiOrder = software.amzn.spapi.Model.orders.v0.Order;

namespace LSB.SellerCentral.Application.Orders.Services;

public interface IOrderSyncService
{
    Task SyncOrderFromSpApiAsync(string amazonOrderId, CancellationToken cancellationToken = default);
    Task SyncRecentOrdersFromSpApiAsync(DateTime fromDate, CancellationToken cancellationToken = default);
    Task<DomainOrder> CreateOrUpdateOrderFromSpApiAsync(SpApiOrder spApiOrder, CancellationToken cancellationToken = default);
}

public class OrderSyncService : IOrderSyncService
{
    private readonly IApplicationDbContext _context;
    private readonly IOrdersApiClient _ordersApiClient;
    private readonly SpApiConfiguration _configuration;
    private readonly ILogger<OrderSyncService> _logger;

    public OrderSyncService(
        IApplicationDbContext context,
        IOrdersApiClient ordersApiClient,
        IOptions<SpApiConfiguration> configuration,
        ILogger<OrderSyncService> logger)
    {
        _context = context;
        _ordersApiClient = ordersApiClient;
        _configuration = configuration.Value;
        _logger = logger;
    }

    public async Task SyncOrderFromSpApiAsync(string amazonOrderId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Syncing order {OrderId} from SP-API", amazonOrderId);

            // Get order from SP-API
            var spApiOrderResponse = await _ordersApiClient.GetOrderAsync(amazonOrderId, cancellationToken);
            
            if (spApiOrderResponse?.Payload == null)
            {
                _logger.LogWarning("No order data received from SP-API for order {OrderId}", amazonOrderId);
                return;
            }

            // Create or update the order
            await CreateOrUpdateOrderFromSpApiAsync(spApiOrderResponse.Payload, cancellationToken);

            _logger.LogInformation("Successfully synced order {OrderId} from SP-API", amazonOrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync order {OrderId} from SP-API", amazonOrderId);
            throw;
        }
    }

    public async Task SyncRecentOrdersFromSpApiAsync(DateTime fromDate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Syncing orders from SP-API since {FromDate}", fromDate);

            var marketplaceIds = _configuration.MarketplaceIds;
            string? nextToken = null;
            int totalOrdersSynced = 0;

            do
            {
                // Get orders from SP-API
                var ordersResponse = await _ordersApiClient.GetOrdersAsync(
                    marketplaceIds: marketplaceIds,
                    lastUpdatedAfter: fromDate,
                    maxResultsPerPage: 50,
                    nextToken: nextToken,
                    cancellationToken: cancellationToken);

                if (ordersResponse?.Payload?.Orders == null)
                    break;

                // Process each order
                foreach (var spApiOrder in ordersResponse.Payload.Orders)
                {
                    try
                    {
                        await CreateOrUpdateOrderFromSpApiAsync(spApiOrder, cancellationToken);
                        totalOrdersSynced++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to process order {OrderId} during bulk sync", spApiOrder.AmazonOrderId);
                        // Continue with other orders
                    }
                }

                nextToken = ordersResponse.Payload.NextToken;

            } while (!string.IsNullOrEmpty(nextToken));

            _logger.LogInformation("Successfully synced {TotalOrders} orders from SP-API", totalOrdersSynced);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync recent orders from SP-API");
            throw;
        }
    }

    public async Task<DomainOrder> CreateOrUpdateOrderFromSpApiAsync(SpApiOrder spApiOrder, CancellationToken cancellationToken = default)
    {
        try
        {
            // Find existing order
            var existingOrder = await _context.Orders
                .FirstOrDefaultAsync(o => o.AmazonOrderId == spApiOrder.AmazonOrderId, cancellationToken);

            if (existingOrder != null)
            {
                // Auto-resolve conflicts: Keep tracking info, update everything else from SP-API
                var preservedTrackingNumber = existingOrder.TrackingNumber;
                var preservedCarrierName = existingOrder.CarrierName;
                var preservedShipmentDate = existingOrder.ShipmentDate;

                // Update with SP-API data
                UpdateOrderFromSpApi(existingOrder, spApiOrder);

                // Restore preserved tracking info if it exists
                if (!string.IsNullOrEmpty(preservedTrackingNumber))
                {
                    existingOrder.TrackingNumber = preservedTrackingNumber;
                    existingOrder.CarrierName = preservedCarrierName;
                    existingOrder.ShipmentDate = preservedShipmentDate;
                }

                existingOrder.LastSyncDate = DateTime.UtcNow;

                _logger.LogInformation("Updated existing order {OrderId} from SP-API", spApiOrder.AmazonOrderId);
                
                await _context.SaveChangesAsync(cancellationToken);
                return existingOrder;
            }
            else
            {
                // Create new order
                var newOrder = CreateOrderFromSpApi(spApiOrder);
                newOrder.LastSyncDate = DateTime.UtcNow;

                // Find or create seller (simplified - assumes first seller for now)
                var seller = await _context.Sellers.FirstOrDefaultAsync(cancellationToken);
                if (seller != null)
                {
                    newOrder.SellerId = seller.Id;
                }

                _context.Orders.Add(newOrder);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Created new order {OrderId} from SP-API", spApiOrder.AmazonOrderId);
                return newOrder;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create or update order {OrderId} from SP-API", spApiOrder.AmazonOrderId);
            throw;
        }
    }

    private void UpdateOrderFromSpApi(DomainOrder existingOrder, SpApiOrder spApiOrder)
    {
        existingOrder.OrderStatus = spApiOrder.OrderStatus.ToString() ?? existingOrder.OrderStatus;
        existingOrder.PurchaseDate = ParseDateTime(spApiOrder.PurchaseDate) ?? existingOrder.PurchaseDate;
        existingOrder.LastUpdateDate = ParseDateTime(spApiOrder.LastUpdateDate);
        existingOrder.TotalAmount = ParseDecimal(spApiOrder.OrderTotal?.Amount) ?? existingOrder.TotalAmount;
        existingOrder.Currency = spApiOrder.OrderTotal?.CurrencyCode ?? existingOrder.Currency;
        existingOrder.FulfillmentChannel = spApiOrder.FulfillmentChannel?.ToString() ?? existingOrder.FulfillmentChannel;
        existingOrder.ShipServiceLevel = spApiOrder.ShipServiceLevel ?? existingOrder.ShipServiceLevel;
        existingOrder.IsBusinessOrder = spApiOrder.IsBusinessOrder;
        existingOrder.EarliestShipDate = ParseDateTime(spApiOrder.EarliestShipDate);
        existingOrder.LatestShipDate = ParseDateTime(spApiOrder.LatestShipDate);
        existingOrder.EarliestDeliveryDate = ParseDateTime(spApiOrder.EarliestDeliveryDate);
        existingOrder.LatestDeliveryDate = ParseDateTime(spApiOrder.LatestDeliveryDate);

        // Update shipping address if available
        if (spApiOrder.ShippingAddress != null)
        {
            existingOrder.ShippingAddress = FormatShippingAddress(spApiOrder.ShippingAddress);
        }
    }

    private DomainOrder CreateOrderFromSpApi(SpApiOrder spApiOrder)
    {
        return new DomainOrder
        {
            AmazonOrderId = spApiOrder.AmazonOrderId ?? string.Empty,
            OrderStatus = spApiOrder.OrderStatus.ToString() ?? "Unknown",
            PurchaseDate = ParseDateTime(spApiOrder.PurchaseDate) ?? DateTime.UtcNow,
            LastUpdateDate = ParseDateTime(spApiOrder.LastUpdateDate),
            TotalAmount = ParseDecimal(spApiOrder.OrderTotal?.Amount) ?? 0,
            Currency = spApiOrder.OrderTotal?.CurrencyCode ?? "USD",
            FulfillmentChannel = spApiOrder.FulfillmentChannel?.ToString(),
            ShipServiceLevel = spApiOrder.ShipServiceLevel,
            IsBusinessOrder = spApiOrder.IsBusinessOrder,
            EarliestShipDate = ParseDateTime(spApiOrder.EarliestShipDate),
            LatestShipDate = ParseDateTime(spApiOrder.LatestShipDate),
            EarliestDeliveryDate = ParseDateTime(spApiOrder.EarliestDeliveryDate),
            LatestDeliveryDate = ParseDateTime(spApiOrder.LatestDeliveryDate),
            ShippingAddress = spApiOrder.ShippingAddress != null ? FormatShippingAddress(spApiOrder.ShippingAddress) : null,
            SellerId = 1 // Will be properly set in the calling method
        };
    }

    private string FormatShippingAddress(Address address)
    {
        var parts = new List<string>();
        
        if (!string.IsNullOrEmpty(address.Name)) parts.Add(address.Name);
        if (!string.IsNullOrEmpty(address.AddressLine1)) parts.Add(address.AddressLine1);
        if (!string.IsNullOrEmpty(address.AddressLine2)) parts.Add(address.AddressLine2);
        if (!string.IsNullOrEmpty(address.City)) parts.Add(address.City);
        if (!string.IsNullOrEmpty(address.StateOrRegion)) parts.Add(address.StateOrRegion);
        if (!string.IsNullOrEmpty(address.PostalCode)) parts.Add(address.PostalCode);
        if (!string.IsNullOrEmpty(address.CountryCode)) parts.Add(address.CountryCode);

        return string.Join(", ", parts);
    }

    private decimal? ParseDecimal(string? value)
    {
        if (string.IsNullOrEmpty(value))
            return null;

        if (decimal.TryParse(value, out var result))
            return result;

        return null;
    }

    private DateTime? ParseDateTime(string? value)
    {
        if (string.IsNullOrEmpty(value))
            return null;

        if (DateTime.TryParse(value, out var result))
            return result;

        return null;
    }
}
