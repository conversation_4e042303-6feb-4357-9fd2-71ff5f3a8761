# PowerShell script to build, publish, and deploy LSB Seller Central Data Collector
# Run this script as Administrator

param(
    [string]$Configuration = "Release",
    [string]$ServiceName = "LSBSellerCentralDataCollector",
    [string]$InstallPath = "C:\Services\LSBSellerCentral",
    [switch]$SkipBuild = $false,
    [switch]$StartService = $true
)

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script must be run as Administrator. Please run PowerShell as Administrator and try again."
    exit 1
}

$ProjectPath = Split-Path $PSScriptRoot -Parent
$PublishPath = Join-Path $ProjectPath "bin\$Configuration\net9.0\publish"
$ServiceExePath = Join-Path $InstallPath "LSB.SellerCentral.Services.exe"

try {
    # Build and publish the project
    if (-not $SkipBuild) {
        Write-Host "Building and publishing the project..."
        
        Push-Location $ProjectPath
        
        # Clean previous builds
        dotnet clean -c $Configuration
        
        # Build and publish
        dotnet publish -c $Configuration --self-contained false --runtime win-x64
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Build failed. Exit code: $LASTEXITCODE"
            Pop-Location
            exit 1
        }
        
        Pop-Location
        Write-Host "Build completed successfully."
    }
    
    # Check if publish directory exists
    if (-not (Test-Path $PublishPath)) {
        Write-Error "Publish directory not found: $PublishPath"
        Write-Host "Please ensure the project builds successfully."
        exit 1
    }
    
    # Stop service if it exists and is running
    $existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($existingService -and $existingService.Status -eq 'Running') {
        Write-Host "Stopping existing service..."
        Stop-Service -Name $ServiceName -Force
        
        # Wait for service to stop
        $timeout = 30
        $elapsed = 0
        while ((Get-Service -Name $ServiceName -ErrorAction SilentlyContinue).Status -eq 'Running' -and $elapsed -lt $timeout) {
            Start-Sleep -Seconds 1
            $elapsed++
        }
    }
    
    # Create installation directory
    if (-not (Test-Path $InstallPath)) {
        Write-Host "Creating installation directory: $InstallPath"
        New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    }
    
    # Copy files to installation directory
    Write-Host "Copying files to installation directory..."
    Copy-Item -Path "$PublishPath\*" -Destination $InstallPath -Recurse -Force
    
    # Install or update the service
    if ($existingService) {
        Write-Host "Service exists. Updating service configuration..."
        sc.exe config $ServiceName binPath= "`"$ServiceExePath`""
    } else {
        Write-Host "Installing new service..."
        & "$PSScriptRoot\install-service.ps1" -ServicePath $ServiceExePath -ServiceName $ServiceName
    }
    
    # Start the service if requested
    if ($StartService) {
        Write-Host "Starting service..."
        Start-Service -Name $ServiceName
        
        # Wait a moment and check status
        Start-Sleep -Seconds 3
        $serviceStatus = Get-Service -Name $ServiceName
        
        if ($serviceStatus.Status -eq 'Running') {
            Write-Host "Service started successfully and is running."
        } else {
            Write-Warning "Service was started but current status is: $($serviceStatus.Status)"
        }
    }
    
    Write-Host ""
    Write-Host "Deployment completed successfully!"
    Write-Host "Service installed at: $InstallPath"
    Write-Host "Service name: $ServiceName"
    Write-Host ""
    Write-Host "Useful commands:"
    Write-Host "  Check status: Get-Service -Name $ServiceName"
    Write-Host "  Start service: Start-Service -Name $ServiceName"
    Write-Host "  Stop service: Stop-Service -Name $ServiceName"
    Write-Host "  View logs: Get-EventLog -LogName Application -Source '$ServiceName' -Newest 10"
    
} catch {
    Write-Error "An error occurred during deployment: $($_.Exception.Message)"
    exit 1
}
