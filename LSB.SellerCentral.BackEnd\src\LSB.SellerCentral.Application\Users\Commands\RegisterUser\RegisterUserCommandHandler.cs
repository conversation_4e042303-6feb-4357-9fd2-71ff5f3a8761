using LSB.SellerCentral.Application.Common.Interfaces;
using LSB.SellerCentral.Application.Common.Models;
using LSB.SellerCentral.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace LSB.SellerCentral.Application.Users.Commands.RegisterUser;

public class RegisterUserCommandHandler : IRequestHandler<RegisterUserCommand, AuthenticationResult>
{
    private readonly IApplicationDbContext _context;
    private readonly IPasswordHashingService _passwordHashingService;
    private readonly IJwtTokenService _jwtTokenService;

    public RegisterUserCommandHandler(
        IApplicationDbContext context,
        IPasswordHashingService passwordHashingService,
        IJwtTokenService jwtTokenService)
    {
        _context = context;
        _passwordHashingService = passwordHashingService;
        _jwtTokenService = jwtTokenService;
    }

    public async Task<AuthenticationResult> Handle(RegisterUserCommand request, CancellationToken cancellationToken)
    {
        // Check if user already exists
        var existingUser = await _context.Users
            .FirstOrDefaultAsync(u => u.Email.ToLower() == request.Email.ToLower(), cancellationToken);

        if (existingUser != null)
        {
            return new AuthenticationResult
            {
                Success = false,
                Errors = new[] { "User with this email already exists." }
            };
        }

        // Create new user
        var user = new User
        {
            Email = request.Email.ToLower(),
            FirstName = request.FirstName,
            LastName = request.LastName,
            PasswordHash = _passwordHashingService.HashPassword(request.Password),
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync(cancellationToken);

        // Generate JWT token
        var token = _jwtTokenService.GenerateToken(user);

        return new AuthenticationResult
        {
            Success = true,
            Token = token,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName
        };
    }
}
