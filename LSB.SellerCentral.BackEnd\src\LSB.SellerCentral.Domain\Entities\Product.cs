using LSB.SellerCentral.Domain.Common;

namespace LSB.SellerCentral.Domain.Entities;

public class Product : BaseEntity
{
    public int SellerId { get; set; }
    public string Sku { get; set; } = string.Empty;
    public string Asin { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public decimal Price { get; set; }
    public string Currency { get; set; } = "USD";
    public int QuantityAvailable { get; set; }
    public int? ReservedQuantity { get; set; }
    public int? InboundQuantity { get; set; }
    public string? Brand { get; set; }
    public string? Category { get; set; }
    public string? ImageUrl { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime? LastSyncDate { get; set; }
    
    // Navigation properties
    public virtual Seller Seller { get; set; } = null!;
    public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();
}
