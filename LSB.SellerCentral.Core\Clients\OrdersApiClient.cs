using LSB.SellerCentral.Core.Authentication;
using LSB.SellerCentral.Core.Configuration;
using LSB.SellerCentral.Core.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using software.amzn.spapi.Api.orders.v0;
using software.amzn.spapi.Model.orders.v0;
using software.amzn.spapi.Auth;
using System.Diagnostics;

namespace LSB.SellerCentral.Core.Clients;

/// <summary>
/// Client for SP-API Orders API operations
/// </summary>
public class OrdersApiClient : BaseSpApiClient, IOrdersApiClient
{
    private readonly OrdersV0Api _ordersApi;

    public OrdersApiClient(
        IOptions<SpApiConfiguration> configuration,
        ISpApiAuthenticationService authenticationService,
        ILogger<OrdersApiClient> logger)
        : base(configuration, authenticationService, logger)
    {
        _ordersApi = CreateOrdersApi();
    }

    public async Task<GetOrdersResponse> GetOrdersAsync(
        List<string> marketplaceIds,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        DateTime? lastUpdatedAfter = null,
        List<string>? orderStatuses = null,
        int? maxResultsPerPage = null,
        string? nextToken = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Logger.LogInformation("Getting orders from SP-API for marketplaces: {MarketplaceIds}", string.Join(", ", marketplaceIds));

        try
        {
            var response = await ExecuteWithRetryAsync(async () =>
            {
                return await _ordersApi.GetOrdersAsync(
                    marketplaceIds: marketplaceIds,
                    createdAfter: createdAfter?.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    createdBefore: createdBefore?.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    lastUpdatedAfter: lastUpdatedAfter?.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    orderStatuses: orderStatuses,
                    fulfillmentChannels: null,
                    paymentMethods: null,
                    buyerEmail: null,
                    sellerOrderId: null,
                    maxResultsPerPage: maxResultsPerPage,
                    easyShipShipmentStatuses: null,
                    electronicInvoiceStatuses: null,
                    nextToken: nextToken,
                    amazonOrderIds: null,
                    actualFulfillmentSupplySourceId: null,
                    isISPU: null,
                    storeChainStoreId: null,
                    itemApprovalTypes: null,
                    itemApprovalStatuses: null,
                    cancellationToken: cancellationToken);
            });

            Logger.LogInformation("Successfully retrieved {OrderCount} orders in {ElapsedMs}ms",
                response?.Payload?.Orders?.Count ?? 0, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get orders from SP-API after {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            throw new SpApiException("Failed to retrieve orders from Amazon SP-API", ex);
        }
    }

    public async Task<GetOrderResponse> GetOrderAsync(string orderId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Logger.LogInformation("Getting order {OrderId} from SP-API", orderId);

        try
        {
            var response = await ExecuteWithRetryAsync(async () =>
            {
                return await _ordersApi.GetOrderAsync(orderId, cancellationToken);
            });

            Logger.LogInformation("Successfully retrieved order {OrderId} in {ElapsedMs}ms",
                orderId, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get order {OrderId} from SP-API after {ElapsedMs}ms",
                orderId, stopwatch.ElapsedMilliseconds);
            throw new SpApiException($"Failed to retrieve order {orderId} from Amazon SP-API", ex);
        }
    }

    public async Task<GetOrderItemsResponse> GetOrderItemsAsync(
        string orderId,
        string? nextToken = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Logger.LogInformation("Getting order items for order {OrderId} from SP-API", orderId);

        try
        {
            var response = await ExecuteWithRetryAsync(async () =>
            {
                return await _ordersApi.GetOrderItemsAsync(orderId, nextToken, cancellationToken);
            });

            Logger.LogInformation("Successfully retrieved {ItemCount} order items for order {OrderId} in {ElapsedMs}ms",
                response?.Payload?.OrderItems?.Count ?? 0, orderId, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get order items for order {OrderId} from SP-API after {ElapsedMs}ms",
                orderId, stopwatch.ElapsedMilliseconds);
            throw new SpApiException($"Failed to retrieve order items for order {orderId} from Amazon SP-API", ex);
        }
    }

    public async Task<ConfirmShipmentResponse> ConfirmShipmentAsync(
        string orderId,
        string marketplaceId,
        PackageDetail packageDetail,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Logger.LogInformation("Confirming shipment for order {OrderId} with tracking {TrackingNumber}",
            orderId, packageDetail?.TrackingNumber);

        try
        {
            var confirmShipmentRequest = new ConfirmShipmentRequest
            {
                PackageDetail = packageDetail,
                MarketplaceId = marketplaceId
            };

            var response = await ExecuteWithRetryAsync(async () =>
            {
                return await _ordersApi.ConfirmShipmentAsync(orderId, confirmShipmentRequest, cancellationToken);
            });

            Logger.LogInformation("Successfully confirmed shipment for order {OrderId} in {ElapsedMs}ms",
                orderId, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to confirm shipment for order {OrderId} after {ElapsedMs}ms",
                orderId, stopwatch.ElapsedMilliseconds);
            throw new SpApiException($"Failed to confirm shipment for order {orderId}", ex);
        }
    }

    /// <summary>
    /// Creates and configures the OrdersV0Api instance
    /// </summary>
    /// <returns>Configured OrdersV0Api instance</returns>
    private OrdersV0Api CreateOrdersApi()
    {
        Logger.LogDebug("Creating OrdersV0Api instance");

        // Create LWA credentials from authentication service
        var lwaCredentials = new LWAAuthorizationCredentials
        {
            ClientId = AuthenticationService.ClientId,
            ClientSecret = AuthenticationService.ClientSecret,
            RefreshToken = AuthenticationService.RefreshToken,
            Endpoint = AuthenticationService.LwaEndpoint
        };

        var ordersApi = new OrdersV0Api.Builder()
            .SetLWAAuthorizationCredentials(lwaCredentials)
            .Build();

        return ordersApi;
    }
}
