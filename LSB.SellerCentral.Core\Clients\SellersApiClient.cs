using LSB.SellerCentral.Core.Authentication;
using LSB.SellerCentral.Core.Configuration;
using LSB.SellerCentral.Core.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using software.amzn.spapi.Api.sellers.v1;
using software.amzn.spapi.Model.sellers.v1;
using software.amzn.spapi.Auth;
using System.Diagnostics;

namespace LSB.SellerCentral.Core.Clients;

/// <summary>
/// Client for SP-API Sellers API operations
/// </summary>
public class SellersApiClient : BaseSpApiClient, ISellersApiClient
{
    private readonly SellersApi _sellersApi;

    public SellersApiClient(
        IOptions<SpApiConfiguration> configuration,
        ISpApiAuthenticationService authenticationService,
        ILogger<SellersApiClient> logger)
        : base(configuration, authenticationService, logger)
    {
        _sellersApi = CreateSellersApi();
    }

    /// <summary>
    /// Gets marketplace participations for the seller
    /// </summary>
    /// <returns>Marketplace participations response</returns>
    /// <exception cref="SpApiException">Thrown when the API call fails</exception>
    public async Task<GetMarketplaceParticipationsResponse> GetMarketplaceParticipationsAsync()
    {
        const string operation = nameof(GetMarketplaceParticipationsAsync);
        ValidateAuthentication();
        
        var stopwatch = Stopwatch.StartNew();
        LogOperationStart(operation);

        try
        {
            // Ensure we have a fresh token
            await AuthenticationService.RefreshTokenIfNeededAsync();

            var response = await Task.Run(() => _sellersApi.GetMarketplaceParticipations());
            
            stopwatch.Stop();
            LogOperationComplete(operation, stopwatch.Elapsed);

            if (response?.Payload == null)
            {
                throw new SpApiException("Received null response from GetMarketplaceParticipations");
            }

            Logger.LogInformation("Successfully retrieved {Count} marketplace participations", 
                response.Payload.Count);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            throw HandleApiException(ex, operation);
        }
    }

    /// <summary>
    /// Gets seller account information (EU only)
    /// </summary>
    /// <returns>Seller account information</returns>
    /// <exception cref="SpApiException">Thrown when the API call fails</exception>
    public async Task<GetAccountResponse> GetAccountAsync()
    {
        const string operation = nameof(GetAccountAsync);
        ValidateAuthentication();
        
        var stopwatch = Stopwatch.StartNew();
        LogOperationStart(operation);

        try
        {
            // Ensure we have a fresh token
            await AuthenticationService.RefreshTokenIfNeededAsync();

            var response = await Task.Run(() => _sellersApi.GetAccount());
            
            stopwatch.Stop();
            LogOperationComplete(operation, stopwatch.Elapsed);

            if (response?.Payload == null)
            {
                throw new SpApiException("Received null response from GetAccount");
            }

            Logger.LogInformation("Successfully retrieved seller account information");

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            throw HandleApiException(ex, operation);
        }
    }

    /// <summary>
    /// Creates and configures the SellersApi instance
    /// </summary>
    /// <returns>Configured SellersApi instance</returns>
    private SellersApi CreateSellersApi()
    {
        Logger.LogDebug("Creating SellersApi instance");

        // Create LWA credentials from authentication service
        var lwaCredentials = new LWAAuthorizationCredentials
        {
            ClientId = AuthenticationService.ClientId,
            ClientSecret = AuthenticationService.ClientSecret,
            RefreshToken = AuthenticationService.RefreshToken,
            Endpoint = AuthenticationService.LwaEndpoint
        };

        var sellersApi = new SellersApi.Builder()
            .SetLWAAuthorizationCredentials(lwaCredentials)
            .Build();

        return sellersApi;
    }

    /// <summary>
    /// Gets marketplace participations for the seller
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Marketplace participations response</returns>
    public async Task<GetMarketplaceParticipationsResponse> GetMarketplaceParticipationsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Getting marketplace participations");

            // For now, use synchronous call wrapped in Task.Run to avoid blocking
            var response = await Task.Run(() => _sellersApi.GetMarketplaceParticipations(), cancellationToken);

            Logger.LogInformation("Successfully retrieved marketplace participations");
            return response;
        }
        catch (Exception ex)
        {
            var customException = HandleApiException(ex, nameof(GetMarketplaceParticipationsAsync));
            throw customException;
        }
    }

    /// <summary>
    /// Disposes the client resources
    /// </summary>
    public void Dispose()
    {
        // The SellersApi doesn't implement IDisposable, so nothing to dispose
        Logger.LogDebug("SellersApiClient disposed");
    }
}
