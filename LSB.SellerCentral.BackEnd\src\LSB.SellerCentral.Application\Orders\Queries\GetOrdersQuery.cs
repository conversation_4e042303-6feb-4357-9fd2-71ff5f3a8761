using LSB.SellerCentral.Application.Common.Interfaces;
using LSB.SellerCentral.Application.Orders.DTOs;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LSB.SellerCentral.Application.Orders.Queries;

public class GetOrdersQuery : IRequest<PaginatedOrdersDto>
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? OrderStatus { get; set; }
    public string? FulfillmentChannel { get; set; }
    public string? SearchTerm { get; set; }
    public bool? HasTracking { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 50;
}

public class GetOrdersQueryHandler : IRequestHandler<GetOrdersQuery, PaginatedOrdersDto>
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<GetOrdersQueryHandler> _logger;

    public GetOrdersQueryHandler(
        IApplicationDbContext context,
        ILogger<GetOrdersQueryHandler> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<PaginatedOrdersDto> Handle(GetOrdersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var query = _context.Orders
                .Include(o => o.Seller)
                .Include(o => o.OrderItems)
                .AsQueryable();

            // Apply filters
            if (request.StartDate.HasValue)
                query = query.Where(o => o.PurchaseDate >= request.StartDate.Value);

            if (request.EndDate.HasValue)
                query = query.Where(o => o.PurchaseDate <= request.EndDate.Value);

            if (!string.IsNullOrEmpty(request.OrderStatus))
                query = query.Where(o => o.OrderStatus == request.OrderStatus);

            if (!string.IsNullOrEmpty(request.FulfillmentChannel))
                query = query.Where(o => o.FulfillmentChannel == request.FulfillmentChannel);

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                query = query.Where(o => 
                    o.AmazonOrderId.Contains(request.SearchTerm) ||
                    (o.CustomerEmail != null && o.CustomerEmail.Contains(request.SearchTerm)) ||
                    (o.TrackingNumber != null && o.TrackingNumber.Contains(request.SearchTerm)));
            }

            if (request.HasTracking.HasValue)
            {
                if (request.HasTracking.Value)
                    query = query.Where(o => !string.IsNullOrEmpty(o.TrackingNumber));
                else
                    query = query.Where(o => string.IsNullOrEmpty(o.TrackingNumber));
            }

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination and ordering
            var orders = await query
                .OrderByDescending(o => o.PurchaseDate)
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .Select(o => new OrderDto
                {
                    Id = o.Id,
                    AmazonOrderId = o.AmazonOrderId,
                    OrderStatus = o.OrderStatus,
                    PurchaseDate = o.PurchaseDate,
                    LastUpdateDate = o.LastUpdateDate,
                    CustomerEmail = o.CustomerEmail,
                    ShippingAddress = o.ShippingAddress,
                    TotalAmount = o.TotalAmount,
                    Currency = o.Currency,
                    FulfillmentChannel = o.FulfillmentChannel,
                    ShipServiceLevel = o.ShipServiceLevel,
                    IsBusinessOrder = o.IsBusinessOrder,
                    EarliestShipDate = o.EarliestShipDate,
                    LatestShipDate = o.LatestShipDate,
                    EarliestDeliveryDate = o.EarliestDeliveryDate,
                    LatestDeliveryDate = o.LatestDeliveryDate,
                    TrackingNumber = o.TrackingNumber,
                    CarrierName = o.CarrierName,
                    ShipmentDate = o.ShipmentDate,
                    LastSyncDate = o.LastSyncDate,
                    SellerId = o.SellerId,
                    SellerName = o.Seller.SellerCentralBusinessName,
                    OrderItems = o.OrderItems.Select(oi => new OrderItemDto
                    {
                        Id = oi.Id,
                        Sku = oi.Sku,
                        Asin = oi.Asin,
                        ProductName = oi.ProductName,
                        Quantity = oi.Quantity,
                        UnitPrice = oi.UnitPrice,
                        TotalPrice = oi.TotalPrice,
                        Currency = oi.Currency,
                        PromotionDiscount = oi.PromotionDiscount,
                        GiftWrapType = oi.GiftWrapType,
                        GiftMessageText = oi.GiftMessageText
                    }).ToList()
                })
                .ToListAsync(cancellationToken);

            var result = new PaginatedOrdersDto
            {
                Orders = orders,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            _logger.LogInformation("Retrieved {OrderCount} orders (page {Page} of {TotalPages})", 
                orders.Count, request.Page, result.TotalPages);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve orders");
            throw;
        }
    }
}
