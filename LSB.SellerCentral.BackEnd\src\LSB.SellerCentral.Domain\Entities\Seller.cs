using LSB.SellerCentral.Domain.Common;

namespace LSB.SellerCentral.Domain.Entities;

public class Seller : BaseEntity
{
    public int UserId { get; set; }
    public string AmazonMarketplaceId { get; set; } = string.Empty;
    public string SellerCentralBusinessName { get; set; } = string.Empty;
    public string? CountryCode { get; set; }
    public string? MarketplaceName { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime? LastSyncDate { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    public virtual ICollection<Order> Orders { get; set; } = new List<Order>();
}
