// Order-related TypeScript interfaces and types

export interface Order {
  id: number;
  amazonOrderId: string;
  orderStatus: string;
  purchaseDate: string;
  lastUpdateDate?: string;
  customerEmail?: string;
  shippingAddress?: string;
  totalAmount: number;
  currency: string;
  fulfillmentChannel?: string;
  shipServiceLevel?: string;
  isBusinessOrder: boolean;
  earliestShipDate?: string;
  latestShipDate?: string;
  earliestDeliveryDate?: string;
  latestDeliveryDate?: string;
  
  // Tracking information
  trackingNumber?: string;
  carrierName?: string;
  shipmentDate?: string;
  lastSyncDate: string;
  
  // Seller information
  sellerId: number;
  sellerName: string;
  
  // Order items
  orderItems: OrderItem[];
}

export interface OrderItem {
  id: number;
  sku: string;
  asin: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  currency: string;
  promotionDiscount?: string;
  giftWrapType?: string;
  giftMessageText?: string;
}

export interface OrderFilter {
  startDate?: string;
  endDate?: string;
  orderStatus?: string;
  fulfillmentChannel?: string;
  searchTerm?: string;
  hasTracking?: boolean;
  page: number;
  pageSize: number;
}

export interface PaginatedOrders {
  orders: Order[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface UpdateTracking {
  trackingNumber: string;
  carrierName?: string;
  shipmentDate?: string;
}

export interface SyncOrders {
  orderIds?: string[];
  syncFromDate?: string;
}

export interface SyncOrdersResult {
  success: boolean;
  ordersSynced: number;
  errors: string[];
  message?: string;
}

export interface OrderStats {
  totalOrders: number;
  pendingOrders: number;
  shippedOrders: number;
  ordersWithTracking: number;
  ordersWithoutTracking: number;
  totalValue: number;
  lastSyncDate: string;
}

// Common order statuses
export const ORDER_STATUSES = {
  PENDING: 'Pending',
  UNSHIPPED: 'Unshipped',
  PARTIALLY_SHIPPED: 'PartiallyShipped',
  SHIPPED: 'Shipped',
  CANCELED: 'Canceled',
  UNFULFILLABLE: 'Unfulfillable'
} as const;

// Common fulfillment channels
export const FULFILLMENT_CHANNELS = {
  MFN: 'MFN', // Merchant Fulfilled Network
  AFN: 'AFN'  // Amazon Fulfilled Network
} as const;

// Common carriers
export const CARRIERS = [
  'UPS',
  'FedEx',
  'USPS',
  'DHL',
  'Amazon Logistics',
  'OnTrac',
  'Lasership',
  'Other'
] as const;

export type OrderStatus = typeof ORDER_STATUSES[keyof typeof ORDER_STATUSES];
export type FulfillmentChannel = typeof FULFILLMENT_CHANNELS[keyof typeof FULFILLMENT_CHANNELS];
export type Carrier = typeof CARRIERS[number];
