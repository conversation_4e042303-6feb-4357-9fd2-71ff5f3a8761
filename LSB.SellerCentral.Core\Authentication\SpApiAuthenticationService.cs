
using LSB.SellerCentral.Core.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RestSharp;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace LSB.SellerCentral.Core.Authentication;

/// <summary>
/// Implementation of SP-API authentication service
/// </summary>
public class SpApiAuthenticationService : ISpApiAuthenticationService
{
    private readonly SpApiConfiguration _configuration;
    private readonly ILogger<SpApiAuthenticationService> _logger;
    private string? _cachedAccessToken;
    private DateTime _tokenExpiryTime;
    private readonly object _tokenLock = new();

    public SpApiAuthenticationService(
        IOptions<SpApiConfiguration> configuration,
        ILogger<SpApiAuthenticationService> logger)
    {
        _configuration = configuration.Value;
        _logger = logger;
    }

    public string ClientId => _configuration.ClientId;
    public string ClientSecret => _configuration.ClientSecret;
    public string RefreshToken => _configuration.RefreshToken;
    public Uri LwaEndpoint => new Uri(_configuration.LwaEndpoint);

    /// <summary>
    /// Gets a fresh access token from LWA
    /// </summary>
    public async Task<string> GetAccessTokenAsync()
    {
        lock (_tokenLock)
        {
            // Return cached token if still valid (with 5-minute buffer)
            if (!string.IsNullOrEmpty(_cachedAccessToken) && 
                DateTime.UtcNow < _tokenExpiryTime.AddMinutes(-5))
            {
                _logger.LogDebug("Returning cached access token");
                return _cachedAccessToken;
            }
        }

        _logger.LogDebug("Requesting new access token from LWA");

        try
        {
            var client = new RestClient(_configuration.LwaEndpoint);
            var request = new RestRequest("", Method.Post);
            
            request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
            request.AddParameter("grant_type", "refresh_token");
            request.AddParameter("refresh_token", _configuration.RefreshToken);
            request.AddParameter("client_id", _configuration.ClientId);
            request.AddParameter("client_secret", _configuration.ClientSecret);

            var response = await client.ExecuteAsync(request);

            if (!response.IsSuccessful)
            {
                _logger.LogError("Failed to get access token. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, response.Content);
                throw new InvalidOperationException($"Failed to get access token: {response.StatusDescription}");
            }

            var tokenResponse = JsonSerializer.Deserialize<LwaTokenResponse>(response.Content!);
            
            if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.AccessToken))
            {
                _logger.LogError("Invalid token response received from LWA");
                throw new InvalidOperationException("Invalid token response received from LWA");
            }

            lock (_tokenLock)
            {
                _cachedAccessToken = tokenResponse.AccessToken;
                _tokenExpiryTime = DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn);
            }

            _logger.LogDebug("Successfully obtained new access token, expires at {ExpiryTime}", _tokenExpiryTime);
            return tokenResponse.AccessToken;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting access token from LWA");
            throw;
        }
    }

    /// <summary>
    /// Validates if the current credentials are properly configured
    /// </summary>
    public bool ValidateCredentials()
    {
        var isValid = _configuration.IsValid();
        
        if (!isValid)
        {
            _logger.LogWarning("SP-API configuration validation failed");
        }
        else
        {
            _logger.LogDebug("SP-API configuration validation passed");
        }

        return isValid;
    }

    /// <summary>
    /// Refreshes the access token if needed
    /// </summary>
    public async Task<bool> RefreshTokenIfNeededAsync()
    {
        try
        {
            lock (_tokenLock)
            {
                // Check if token needs refresh (within 10 minutes of expiry)
                if (!string.IsNullOrEmpty(_cachedAccessToken) && 
                    DateTime.UtcNow < _tokenExpiryTime.AddMinutes(-10))
                {
                    return true; // Token is still valid
                }
            }

            // Force refresh
            await GetAccessTokenAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh access token");
            return false;
        }
    }

    /// <summary>
    /// LWA token response model
    /// </summary>
    private class LwaTokenResponse
    {
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;

        [JsonPropertyName("token_type")]
        public string TokenType { get; set; } = string.Empty;

        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        [JsonPropertyName("refresh_token")]
        public string RefreshToken { get; set; } = string.Empty;
    }
}
