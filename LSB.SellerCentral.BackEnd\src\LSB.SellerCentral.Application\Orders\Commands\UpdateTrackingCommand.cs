using LSB.SellerCentral.Application.Common.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LSB.SellerCentral.Application.Orders.Commands;

public class UpdateTrackingCommand : IRequest<bool>
{
    public int OrderId { get; set; }
    public string TrackingNumber { get; set; } = string.Empty;
    public string? CarrierName { get; set; }
    public DateTime? ShipmentDate { get; set; }
}

public class UpdateTrackingCommandHandler : IRequestHandler<UpdateTrackingCommand, bool>
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<UpdateTrackingCommandHandler> _logger;

    public UpdateTrackingCommandHandler(
        IApplicationDbContext context,
        ILogger<UpdateTrackingCommandHandler> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<bool> Handle(UpdateTrackingCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var order = await _context.Orders
                .FirstOrDefaultAsync(o => o.Id == request.OrderId, cancellationToken);

            if (order == null)
            {
                _logger.LogWarning("Order with ID {OrderId} not found", request.OrderId);
                return false;
            }

            // Update tracking information
            order.TrackingNumber = request.TrackingNumber;
            order.CarrierName = request.CarrierName;
            order.ShipmentDate = request.ShipmentDate ?? DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated tracking for order {OrderId} ({AmazonOrderId}): {TrackingNumber}",
                order.Id, order.AmazonOrderId, request.TrackingNumber);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update tracking for order {OrderId}", request.OrderId);
            throw;
        }
    }
}
