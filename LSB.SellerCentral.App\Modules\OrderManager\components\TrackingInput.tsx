import React, { useState, useRef, useEffect } from 'react';
import { orderService } from '../services/orderService';

interface TrackingInputProps {
  orderId: number;
  currentTracking?: string;
  currentCarrier?: string;
  onUpdate: (orderId: number, trackingNumber: string, carrierName?: string) => Promise<void>;
}

export const TrackingInput: React.FC<TrackingInputProps> = ({
  orderId,
  currentTracking = '',
  currentCarrier = '',
  onUpdate
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [trackingNumber, setTrackingNumber] = useState(currentTracking);
  const [carrierName, setCarrierName] = useState(currentCarrier);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const carrierOptions = orderService.getCarrierOptions();

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // Handle click outside to cancel editing
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        handleCancel();
      }
    };

    if (isEditing) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isEditing]);

  const handleEdit = () => {
    setIsEditing(true);
    setError(null);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setTrackingNumber(currentTracking);
    setCarrierName(currentCarrier);
    setError(null);
  };

  const handleSave = async () => {
    if (!trackingNumber.trim()) {
      setError('Tracking number is required');
      return;
    }

    try {
      setIsUpdating(true);
      setError(null);
      
      await onUpdate(orderId, trackingNumber.trim(), carrierName || undefined);
      
      setIsEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update tracking');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleSave();
    } else if (event.key === 'Escape') {
      event.preventDefault();
      handleCancel();
    }
  };

  const handleTrackingChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTrackingNumber(event.target.value);
    setError(null);
  };

  const handleCarrierChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setCarrierName(event.target.value);
  };

  if (!isEditing) {
    return (
      <div className="tracking-display" onClick={handleEdit}>
        {currentTracking ? (
          <div className="tracking-info">
            <div className="tracking-number" title="Click to edit">
              {currentTracking}
            </div>
            {currentCarrier && (
              <div className="carrier-name">
                {currentCarrier}
              </div>
            )}
          </div>
        ) : (
          <div className="no-tracking" title="Click to add tracking">
            <span className="add-tracking-text">+ Add Tracking</span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div ref={containerRef} className="tracking-input-container">
      <div className="tracking-input-form">
        <input
          ref={inputRef}
          type="text"
          value={trackingNumber}
          onChange={handleTrackingChange}
          onKeyDown={handleKeyDown}
          placeholder="Enter tracking number"
          className={`tracking-input ${error ? 'error' : ''}`}
          disabled={isUpdating}
        />
        
        <select
          value={carrierName}
          onChange={handleCarrierChange}
          className="carrier-select"
          disabled={isUpdating}
        >
          <option value="">Select Carrier</option>
          {carrierOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        <div className="tracking-input-actions">
          <button
            type="button"
            onClick={handleSave}
            disabled={isUpdating || !trackingNumber.trim()}
            className="save-button"
            title="Save (Enter)"
          >
            {isUpdating ? '⏳' : '✓'}
          </button>
          <button
            type="button"
            onClick={handleCancel}
            disabled={isUpdating}
            className="cancel-button"
            title="Cancel (Esc)"
          >
            ✕
          </button>
        </div>
      </div>

      {error && (
        <div className="tracking-input-error">
          {error}
        </div>
      )}
    </div>
  );
};

export default TrackingInput;
