import { 
  Order, 
  OrderFilter, 
  PaginatedOrders, 
  UpdateTracking, 
  SyncOrders, 
  SyncOrdersResult,
  OrderStats 
} from '../types/order.types';

// Base API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://localhost:7001/api';

class OrderService {
  private async makeRequest<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('authToken');
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  /**
   * Get orders with filtering and pagination
   */
  async getOrders(filter: Partial<OrderFilter> = {}): Promise<PaginatedOrders> {
    const params = new URLSearchParams();
    
    if (filter.startDate) params.append('startDate', filter.startDate);
    if (filter.endDate) params.append('endDate', filter.endDate);
    if (filter.orderStatus) params.append('orderStatus', filter.orderStatus);
    if (filter.fulfillmentChannel) params.append('fulfillmentChannel', filter.fulfillmentChannel);
    if (filter.searchTerm) params.append('searchTerm', filter.searchTerm);
    if (filter.hasTracking !== undefined) params.append('hasTracking', filter.hasTracking.toString());
    if (filter.page) params.append('page', filter.page.toString());
    if (filter.pageSize) params.append('pageSize', filter.pageSize.toString());

    const queryString = params.toString();
    const endpoint = `/order${queryString ? `?${queryString}` : ''}`;
    
    return this.makeRequest<PaginatedOrders>(endpoint);
  }

  /**
   * Get a specific order by ID
   */
  async getOrderById(id: number, refresh: boolean = false): Promise<Order> {
    const params = refresh ? '?refresh=true' : '';
    return this.makeRequest<Order>(`/order/${id}${params}`);
  }

  /**
   * Update tracking information for an order
   */
  async updateTracking(orderId: number, tracking: UpdateTracking): Promise<{ message: string }> {
    return this.makeRequest<{ message: string }>(`/order/${orderId}/tracking`, {
      method: 'PUT',
      body: JSON.stringify(tracking),
    });
  }

  /**
   * Sync orders from Amazon SP-API
   */
  async syncOrders(syncData?: SyncOrders): Promise<SyncOrdersResult> {
    return this.makeRequest<SyncOrdersResult>('/order/sync', {
      method: 'POST',
      body: JSON.stringify(syncData || {}),
    });
  }

  /**
   * Get order statistics (if implemented in backend)
   */
  async getOrderStats(): Promise<OrderStats> {
    return this.makeRequest<OrderStats>('/order/stats');
  }

  /**
   * Export orders to CSV (if implemented in backend)
   */
  async exportOrders(filter: Partial<OrderFilter> = {}): Promise<Blob> {
    const params = new URLSearchParams();
    
    if (filter.startDate) params.append('startDate', filter.startDate);
    if (filter.endDate) params.append('endDate', filter.endDate);
    if (filter.orderStatus) params.append('orderStatus', filter.orderStatus);
    if (filter.fulfillmentChannel) params.append('fulfillmentChannel', filter.fulfillmentChannel);
    if (filter.searchTerm) params.append('searchTerm', filter.searchTerm);
    if (filter.hasTracking !== undefined) params.append('hasTracking', filter.hasTracking.toString());

    const queryString = params.toString();
    const endpoint = `/order/export${queryString ? `?${queryString}` : ''}`;
    
    const token = localStorage.getItem('authToken');
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      throw new Error(`Export failed: ${response.status}`);
    }

    return response.blob();
  }

  /**
   * Bulk update tracking for multiple orders
   */
  async bulkUpdateTracking(updates: Array<{ orderId: number; tracking: UpdateTracking }>): Promise<{
    successful: number;
    failed: number;
    errors: string[];
  }> {
    const results = {
      successful: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (const update of updates) {
      try {
        await this.updateTracking(update.orderId, update.tracking);
        results.successful++;
      } catch (error) {
        results.failed++;
        results.errors.push(`Order ${update.orderId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return results;
  }

  /**
   * Get order status options for filtering
   */
  getOrderStatusOptions(): Array<{ value: string; label: string }> {
    return [
      { value: '', label: 'All Statuses' },
      { value: 'Pending', label: 'Pending' },
      { value: 'Unshipped', label: 'Unshipped' },
      { value: 'PartiallyShipped', label: 'Partially Shipped' },
      { value: 'Shipped', label: 'Shipped' },
      { value: 'Canceled', label: 'Canceled' },
      { value: 'Unfulfillable', label: 'Unfulfillable' }
    ];
  }

  /**
   * Get fulfillment channel options for filtering
   */
  getFulfillmentChannelOptions(): Array<{ value: string; label: string }> {
    return [
      { value: '', label: 'All Channels' },
      { value: 'MFN', label: 'Merchant Fulfilled (MFN)' },
      { value: 'AFN', label: 'Amazon Fulfilled (AFN)' }
    ];
  }

  /**
   * Get carrier options for tracking
   */
  getCarrierOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'UPS', label: 'UPS' },
      { value: 'FedEx', label: 'FedEx' },
      { value: 'USPS', label: 'USPS' },
      { value: 'DHL', label: 'DHL' },
      { value: 'Amazon Logistics', label: 'Amazon Logistics' },
      { value: 'OnTrac', label: 'OnTrac' },
      { value: 'Lasership', label: 'Lasership' },
      { value: 'Other', label: 'Other' }
    ];
  }
}

// Export singleton instance
export const orderService = new OrderService();
export default orderService;
