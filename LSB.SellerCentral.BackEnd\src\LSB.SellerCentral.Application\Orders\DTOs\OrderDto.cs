namespace LSB.SellerCentral.Application.Orders.DTOs;

public class OrderDto
{
    public int Id { get; set; }
    public string AmazonOrderId { get; set; } = string.Empty;
    public string OrderStatus { get; set; } = string.Empty;
    public DateTime PurchaseDate { get; set; }
    public DateTime? LastUpdateDate { get; set; }
    public string? CustomerEmail { get; set; }
    public string? ShippingAddress { get; set; }
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = "USD";
    public string? FulfillmentChannel { get; set; }
    public string? ShipServiceLevel { get; set; }
    public bool IsBusinessOrder { get; set; }
    public DateTime? EarliestShipDate { get; set; }
    public DateTime? LatestShipDate { get; set; }
    public DateTime? EarliestDeliveryDate { get; set; }
    public DateTime? LatestDeliveryDate { get; set; }
    
    // Tracking information
    public string? TrackingNumber { get; set; }
    public string? CarrierName { get; set; }
    public DateTime? ShipmentDate { get; set; }
    public DateTime LastSyncDate { get; set; }
    
    // Seller information
    public int SellerId { get; set; }
    public string SellerName { get; set; } = string.Empty;
    
    // Order items
    public List<OrderItemDto> OrderItems { get; set; } = new List<OrderItemDto>();
}

public class OrderItemDto
{
    public int Id { get; set; }
    public string Sku { get; set; } = string.Empty;
    public string Asin { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public string Currency { get; set; } = "USD";
    public string? PromotionDiscount { get; set; }
    public string? GiftWrapType { get; set; }
    public string? GiftMessageText { get; set; }
}

public class OrderFilterDto
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? OrderStatus { get; set; }
    public string? FulfillmentChannel { get; set; }
    public string? SearchTerm { get; set; } // For searching by Order ID, customer email, etc.
    public bool? HasTracking { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 50;
}

public class PaginatedOrdersDto
{
    public List<OrderDto> Orders { get; set; } = new List<OrderDto>();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => Page < TotalPages;
    public bool HasPreviousPage => Page > 1;
}

public class UpdateTrackingDto
{
    public string TrackingNumber { get; set; } = string.Empty;
    public string? CarrierName { get; set; }
    public DateTime? ShipmentDate { get; set; }
}

public class SyncOrdersDto
{
    public List<string>? OrderIds { get; set; } // If null, sync all orders
    public DateTime? SyncFromDate { get; set; } // Only sync orders modified after this date
}

public class OrderStatsDto
{
    public int TotalOrders { get; set; }
    public int PendingOrders { get; set; }
    public int ShippedOrders { get; set; }
    public int OrdersWithTracking { get; set; }
    public int OrdersWithoutTracking { get; set; }
    public decimal TotalValue { get; set; }
    public DateTime LastSyncDate { get; set; }
}
