using MediatR;
using LSB.SellerCentral.Application.Common.Models;

namespace LSB.SellerCentral.Application.Users.Commands.RegisterUser;

public class RegisterUserCommand : IRequest<AuthenticationResult>
{
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
}
