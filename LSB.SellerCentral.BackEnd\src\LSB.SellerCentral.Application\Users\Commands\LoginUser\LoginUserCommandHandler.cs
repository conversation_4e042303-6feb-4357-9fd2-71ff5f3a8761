using LSB.SellerCentral.Application.Common.Interfaces;
using LSB.SellerCentral.Application.Common.Models;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace LSB.SellerCentral.Application.Users.Commands.LoginUser;

public class LoginUserCommandHandler : IRequestHandler<LoginUserCommand, AuthenticationResult>
{
    private readonly IApplicationDbContext _context;
    private readonly IPasswordHashingService _passwordHashingService;
    private readonly IJwtTokenService _jwtTokenService;

    public LoginUserCommandHandler(
        IApplicationDbContext context,
        IPasswordHashingService passwordHashingService,
        IJwtTokenService jwtTokenService)
    {
        _context = context;
        _passwordHashingService = passwordHashingService;
        _jwtTokenService = jwtTokenService;
    }

    public async Task<AuthenticationResult> Handle(LoginUserCommand request, CancellationToken cancellationToken)
    {
        // Find user by email
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Email.ToLower() == request.Email.ToLower(), cancellationToken);

        if (user == null)
        {
            return new AuthenticationResult
            {
                Success = false,
                Errors = new[] { "Invalid email or password." }
            };
        }

        // Check if user is active
        if (!user.IsActive)
        {
            return new AuthenticationResult
            {
                Success = false,
                Errors = new[] { "User account is deactivated." }
            };
        }

        // Verify password
        if (!_passwordHashingService.VerifyPassword(request.Password, user.PasswordHash))
        {
            return new AuthenticationResult
            {
                Success = false,
                Errors = new[] { "Invalid email or password." }
            };
        }

        // Update last login date
        user.LastLoginDate = DateTime.UtcNow;
        await _context.SaveChangesAsync(cancellationToken);

        // Generate JWT token
        var token = _jwtTokenService.GenerateToken(user);

        return new AuthenticationResult
        {
            Success = true,
            Token = token,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName
        };
    }
}
