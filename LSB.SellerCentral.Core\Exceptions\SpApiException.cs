namespace LSB.SellerCentral.Core.Exceptions;

/// <summary>
/// Base exception for SP-API related errors
/// </summary>
public class SpApiException : Exception
{
    /// <summary>
    /// The error code returned by the SP-API
    /// </summary>
    public int ErrorCode { get; }

    /// <summary>
    /// The raw error content returned by the SP-API
    /// </summary>
    public string? ErrorContent { get; }

    /// <summary>
    /// Initializes a new instance of the SpApiException class
    /// </summary>
    /// <param name="message">The error message</param>
    public SpApiException(string message) : base(message)
    {
        ErrorCode = 0;
        ErrorContent = null;
    }

    /// <summary>
    /// Initializes a new instance of the SpApiException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="innerException">The inner exception</param>
    public SpApiException(string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = 0;
        ErrorContent = null;
    }

    /// <summary>
    /// Initializes a new instance of the SpApiException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errorCode">The SP-API error code</param>
    /// <param name="errorContent">The raw error content</param>
    public SpApiException(string message, int errorCode, string? errorContent) : base(message)
    {
        ErrorCode = errorCode;
        ErrorContent = errorContent;
    }

    /// <summary>
    /// Initializes a new instance of the SpApiException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errorCode">The SP-API error code</param>
    /// <param name="errorContent">The raw error content</param>
    /// <param name="innerException">The inner exception</param>
    public SpApiException(string message, int errorCode, string? errorContent, Exception innerException) 
        : base(message, innerException)
    {
        ErrorCode = errorCode;
        ErrorContent = errorContent;
    }
}

/// <summary>
/// Exception thrown when SP-API requests timeout
/// </summary>
public class SpApiTimeoutException : SpApiException
{
    /// <summary>
    /// Initializes a new instance of the SpApiTimeoutException class
    /// </summary>
    /// <param name="message">The error message</param>
    public SpApiTimeoutException(string message) : base(message)
    {
    }

    /// <summary>
    /// Initializes a new instance of the SpApiTimeoutException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="innerException">The inner exception</param>
    public SpApiTimeoutException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// Exception thrown when SP-API rate limits are exceeded
/// </summary>
public class SpApiRateLimitException : SpApiException
{
    /// <summary>
    /// The time to wait before retrying the request
    /// </summary>
    public TimeSpan? RetryAfter { get; }

    /// <summary>
    /// Initializes a new instance of the SpApiRateLimitException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="retryAfter">The time to wait before retrying</param>
    public SpApiRateLimitException(string message, TimeSpan? retryAfter = null) : base(message)
    {
        RetryAfter = retryAfter;
    }

    /// <summary>
    /// Initializes a new instance of the SpApiRateLimitException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="retryAfter">The time to wait before retrying</param>
    /// <param name="innerException">The inner exception</param>
    public SpApiRateLimitException(string message, TimeSpan? retryAfter, Exception innerException) 
        : base(message, innerException)
    {
        RetryAfter = retryAfter;
    }
}

/// <summary>
/// Exception thrown when SP-API authentication fails
/// </summary>
public class SpApiAuthenticationException : SpApiException
{
    /// <summary>
    /// Initializes a new instance of the SpApiAuthenticationException class
    /// </summary>
    /// <param name="message">The error message</param>
    public SpApiAuthenticationException(string message) : base(message)
    {
    }

    /// <summary>
    /// Initializes a new instance of the SpApiAuthenticationException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="innerException">The inner exception</param>
    public SpApiAuthenticationException(string message, Exception innerException) : base(message, innerException)
    {
    }
}
