﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.7" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="software.amzn.spapi" Version="1.1.2" />
  </ItemGroup>

</Project>
