using LSB.SellerCentral.Services;
using LSB.SellerCentral.Core.Extensions;

var builder = Host.CreateApplicationBuilder(args);

// Configure Windows Service support
builder.Services.AddWindowsService(options =>
{
    options.ServiceName = "LSB Seller Central Data Collector";
});

// Add SP-API Core services
builder.Services.AddSpApiServices(builder.Configuration);

// Add the data collector worker service
builder.Services.AddHostedService<DataCollectorWorker>();

var host = builder.Build();
host.Run();
