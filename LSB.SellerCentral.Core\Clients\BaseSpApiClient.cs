using LSB.SellerCentral.Core.Authentication;
using LSB.SellerCentral.Core.Configuration;
using LSB.SellerCentral.Core.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Extensions.Http;
using software.amzn.spapi.Client;
using System.Net;

namespace LSB.SellerCentral.Core.Clients;

/// <summary>
/// Base class for all SP-API client implementations
/// </summary>
public abstract class BaseSpApiClient
{
    protected readonly SpApiConfiguration Configuration;
    protected readonly ISpApiAuthenticationService AuthenticationService;
    protected readonly ILogger Logger;
    protected readonly IAsyncPolicy<HttpResponseMessage> RetryPolicy;

    protected BaseSpApiClient(
        IOptions<SpApiConfiguration> configuration,
        ISpApiAuthenticationService authenticationService,
        ILogger logger)
    {
        Configuration = configuration.Value;
        AuthenticationService = authenticationService;
        Logger = logger;
        RetryPolicy = CreateRetryPolicy();
    }

    /// <summary>
    /// Creates a configured API configuration for SP-API clients
    /// </summary>
    /// <returns>Configured API configuration</returns>
    protected virtual software.amzn.spapi.Client.Configuration CreateApiConfiguration()
    {
        Logger.LogDebug("Creating SP-API configuration for {ClientType}", GetType().Name);

        var config = new software.amzn.spapi.Client.Configuration
        {
            BasePath = Configuration.ApiEndpoint,
            UserAgent = $"{Configuration.ApplicationName}/{Configuration.ApplicationVersion}",
            Timeout = Configuration.RequestTimeoutSeconds * 1000 // Convert to milliseconds
        };

        return config;
    }

    /// <summary>
    /// Creates a retry policy for API calls
    /// </summary>
    /// <returns>Configured retry policy</returns>
    protected virtual IAsyncPolicy<HttpResponseMessage> CreateRetryPolicy()
    {
        var retryPolicy = Policy
            .HandleResult<HttpResponseMessage>(r => !r.IsSuccessStatusCode && ShouldRetry(r.StatusCode))
            .Or<HttpRequestException>()
            .Or<TaskCanceledException>()
            .WaitAndRetryAsync(
                retryCount: Configuration.MaxRetryAttempts,
                sleepDurationProvider: retryAttempt => Configuration.UseExponentialBackoff
                    ? TimeSpan.FromMilliseconds(Configuration.RetryDelayMs * Math.Pow(2, retryAttempt - 1))
                    : TimeSpan.FromMilliseconds(Configuration.RetryDelayMs),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    Logger.LogWarning(
                        "Retry attempt {RetryCount} for {ClientType} after {Delay}ms. Reason: {Reason}",
                        retryCount,
                        GetType().Name,
                        timespan.TotalMilliseconds,
                        outcome.Exception?.Message ?? outcome.Result?.StatusCode.ToString());
                });

        return retryPolicy;
    }

    /// <summary>
    /// Determines if a request should be retried based on the status code
    /// </summary>
    /// <param name="statusCode">HTTP status code</param>
    /// <returns>True if the request should be retried</returns>
    protected virtual bool ShouldRetry(HttpStatusCode statusCode)
    {
        return statusCode switch
        {
            HttpStatusCode.TooManyRequests => true,
            HttpStatusCode.InternalServerError => true,
            HttpStatusCode.BadGateway => true,
            HttpStatusCode.ServiceUnavailable => true,
            HttpStatusCode.GatewayTimeout => true,
            HttpStatusCode.RequestTimeout => true,
            _ => false
        };
    }

    /// <summary>
    /// Handles API exceptions and converts them to appropriate custom exceptions
    /// </summary>
    /// <param name="ex">The exception to handle</param>
    /// <param name="operation">The operation that failed</param>
    /// <returns>Appropriate custom exception</returns>
    protected virtual Exception HandleApiException(Exception ex, string operation)
    {
        Logger.LogError(ex, "Error in {ClientType}.{Operation}", GetType().Name, operation);

        return ex switch
        {
            ApiException apiEx => new SpApiException(
                $"SP-API error in {operation}: {apiEx.Message}",
                apiEx.ErrorCode,
                apiEx.ErrorContent?.ToString(),
                apiEx),
            HttpRequestException httpEx => new SpApiException(
                $"HTTP error in {operation}: {httpEx.Message}",
                0,
                null,
                httpEx),
            TaskCanceledException timeoutEx => new SpApiTimeoutException(
                $"Timeout in {operation}: {timeoutEx.Message}",
                timeoutEx),
            _ => new SpApiException(
                $"Unexpected error in {operation}: {ex.Message}",
                0,
                null,
                ex)
        };
    }

    /// <summary>
    /// Validates that the authentication service is properly configured
    /// </summary>
    /// <exception cref="InvalidOperationException">Thrown when authentication is not properly configured</exception>
    protected virtual void ValidateAuthentication()
    {
        if (!AuthenticationService.ValidateCredentials())
        {
            throw new InvalidOperationException("SP-API authentication is not properly configured");
        }
    }

    /// <summary>
    /// Logs the start of an API operation
    /// </summary>
    /// <param name="operation">The operation name</param>
    /// <param name="parameters">Optional parameters to log</param>
    protected virtual void LogOperationStart(string operation, object? parameters = null)
    {
        if (parameters != null)
        {
            Logger.LogDebug("Starting {ClientType}.{Operation} with parameters: {@Parameters}",
                GetType().Name, operation, parameters);
        }
        else
        {
            Logger.LogDebug("Starting {ClientType}.{Operation}", GetType().Name, operation);
        }
    }

    /// <summary>
    /// Logs the completion of an API operation
    /// </summary>
    /// <param name="operation">The operation name</param>
    /// <param name="duration">The operation duration</param>
    protected virtual void LogOperationComplete(string operation, TimeSpan duration)
    {
        Logger.LogDebug("Completed {ClientType}.{Operation} in {Duration}ms",
            GetType().Name, operation, duration.TotalMilliseconds);
    }
}
