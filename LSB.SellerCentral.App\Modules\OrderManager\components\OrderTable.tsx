import React, { useState } from 'react';
import { Order } from '../types/order.types';
import { TrackingInput } from './TrackingInput';
import { Pagination } from './Pagination';

interface OrderTableProps {
  orders: Order[];
  loading: boolean;
  onTrackingUpdate: (orderId: number, trackingNumber: string, carrierName?: string) => Promise<void>;
  onRefreshOrder: (orderId: number) => Promise<void>;
  pagination: {
    page: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  onPageChange: (page: number) => void;
}

export const OrderTable: React.FC<OrderTableProps> = ({
  orders,
  loading,
  onTrackingUpdate,
  onRefreshOrder,
  pagination,
  onPageChange
}) => {
  const [expandedOrders, setExpandedOrders] = useState<Set<number>>(new Set());

  const toggleOrderExpansion = (orderId: number) => {
    const newExpanded = new Set(expandedOrders);
    if (newExpanded.has(orderId)) {
      newExpanded.delete(orderId);
    } else {
      newExpanded.add(orderId);
    }
    setExpandedOrders(newExpanded);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'status-pending';
      case 'unshipped': return 'status-unshipped';
      case 'partiallyshipped': return 'status-partial';
      case 'shipped': return 'status-shipped';
      case 'canceled': return 'status-canceled';
      default: return 'status-default';
    }
  };

  if (loading) {
    return (
      <div className="order-table-loading">
        <div className="loading-spinner"></div>
        <p>Loading orders...</p>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="order-table-empty">
        <p>No orders found matching your criteria.</p>
      </div>
    );
  }

  return (
    <div className="order-table-container">
      <div className="order-table-wrapper">
        <table className="order-table">
          <thead>
            <tr>
              <th></th> {/* Expand/collapse column */}
              <th>Order ID</th>
              <th>Status</th>
              <th>Purchase Date</th>
              <th>Customer</th>
              <th>Total</th>
              <th>Fulfillment</th>
              <th>Tracking</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {orders.map((order) => (
              <React.Fragment key={order.id}>
                <tr className="order-row">
                  <td>
                    <button
                      className="expand-button"
                      onClick={() => toggleOrderExpansion(order.id)}
                      aria-label={expandedOrders.has(order.id) ? 'Collapse' : 'Expand'}
                    >
                      {expandedOrders.has(order.id) ? '▼' : '▶'}
                    </button>
                  </td>
                  <td>
                    <div className="order-id">
                      <strong>{order.amazonOrderId}</strong>
                      <small>ID: {order.id}</small>
                    </div>
                  </td>
                  <td>
                    <span className={`status-badge ${getStatusBadgeClass(order.orderStatus)}`}>
                      {order.orderStatus}
                    </span>
                  </td>
                  <td>{formatDate(order.purchaseDate)}</td>
                  <td>
                    <div className="customer-info">
                      {order.customerEmail && (
                        <div className="customer-email">{order.customerEmail}</div>
                      )}
                      {order.isBusinessOrder && (
                        <span className="business-badge">Business</span>
                      )}
                    </div>
                  </td>
                  <td>
                    <strong>{formatCurrency(order.totalAmount, order.currency)}</strong>
                  </td>
                  <td>
                    <span className={`fulfillment-badge ${order.fulfillmentChannel?.toLowerCase()}`}>
                      {order.fulfillmentChannel || 'N/A'}
                    </span>
                  </td>
                  <td>
                    <TrackingInput
                      orderId={order.id}
                      currentTracking={order.trackingNumber}
                      currentCarrier={order.carrierName}
                      onUpdate={onTrackingUpdate}
                    />
                  </td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="refresh-button"
                        onClick={() => onRefreshOrder(order.id)}
                        title="Refresh from Amazon"
                      >
                        🔄
                      </button>
                    </div>
                  </td>
                </tr>
                
                {expandedOrders.has(order.id) && (
                  <tr className="order-details-row">
                    <td colSpan={9}>
                      <div className="order-details">
                        <div className="order-details-section">
                          <h4>Order Information</h4>
                          <div className="details-grid">
                            <div className="detail-item">
                              <label>Ship Service Level:</label>
                              <span>{order.shipServiceLevel || 'N/A'}</span>
                            </div>
                            <div className="detail-item">
                              <label>Earliest Ship Date:</label>
                              <span>{order.earliestShipDate ? formatDate(order.earliestShipDate) : 'N/A'}</span>
                            </div>
                            <div className="detail-item">
                              <label>Latest Ship Date:</label>
                              <span>{order.latestShipDate ? formatDate(order.latestShipDate) : 'N/A'}</span>
                            </div>
                            <div className="detail-item">
                              <label>Last Sync:</label>
                              <span>{formatDate(order.lastSyncDate)}</span>
                            </div>
                          </div>
                        </div>

                        {order.shippingAddress && (
                          <div className="order-details-section">
                            <h4>Shipping Address</h4>
                            <p>{order.shippingAddress}</p>
                          </div>
                        )}

                        {order.orderItems.length > 0 && (
                          <div className="order-details-section">
                            <h4>Order Items ({order.orderItems.length})</h4>
                            <div className="order-items">
                              {order.orderItems.map((item) => (
                                <div key={item.id} className="order-item">
                                  <div className="item-info">
                                    <strong>{item.productName}</strong>
                                    <div className="item-details">
                                      <span>SKU: {item.sku}</span>
                                      <span>ASIN: {item.asin}</span>
                                      <span>Qty: {item.quantity}</span>
                                      <span>Price: {formatCurrency(item.totalPrice, item.currency)}</span>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      <Pagination
        currentPage={pagination.page}
        totalPages={pagination.totalPages}
        hasNextPage={pagination.hasNextPage}
        hasPreviousPage={pagination.hasPreviousPage}
        onPageChange={onPageChange}
      />
    </div>
  );
};

export default OrderTable;
