{"format": 1, "restore": {"D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Infrastructure\\LSB.SellerCentral.Infrastructure.csproj": {}}, "projects": {"D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Application\\LSB.SellerCentral.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Application\\LSB.SellerCentral.Application.csproj", "projectName": "LSB.SellerCentral.Application", "projectPath": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Application\\LSB.SellerCentral.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Progress\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 18.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 18.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Domain\\LSB.SellerCentral.Domain.csproj": {"projectPath": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Domain\\LSB.SellerCentral.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[12.0.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.9.0, )"}, "MediatR": {"target": "Package", "version": "[13.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Domain\\LSB.SellerCentral.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Domain\\LSB.SellerCentral.Domain.csproj", "projectName": "LSB.SellerCentral.Domain", "projectPath": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Domain\\LSB.SellerCentral.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Progress\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 18.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 18.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Infrastructure\\LSB.SellerCentral.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Infrastructure\\LSB.SellerCentral.Infrastructure.csproj", "projectName": "LSB.SellerCentral.Infrastructure", "projectPath": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Infrastructure\\LSB.SellerCentral.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Progress\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 18.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 18.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Application\\LSB.SellerCentral.Application.csproj": {"projectPath": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Application\\LSB.SellerCentral.Application.csproj"}, "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Domain\\LSB.SellerCentral.Domain.csproj": {"projectPath": "D:\\Sources\\LSBSellerCentral\\LSB.SellerCentral.BackEnd\\src\\LSB.SellerCentral.Domain\\LSB.SellerCentral.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.0.1, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}