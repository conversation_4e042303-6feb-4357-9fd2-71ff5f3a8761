using LSB.SellerCentral.Application.Orders.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace LSB.SellerCentral.Application.Orders.Commands;

public class SyncOrdersCommand : IRequest<SyncOrdersResult>
{
    public List<string>? OrderIds { get; set; }
    public DateTime? SyncFromDate { get; set; }
}

public class SyncOrdersResult
{
    public bool Success { get; set; }
    public int OrdersSynced { get; set; }
    public List<string> Errors { get; set; } = new List<string>();
    public string? Message { get; set; }
}

public class SyncOrdersCommandHandler : IRequestHandler<SyncOrdersCommand, SyncOrdersResult>
{
    private readonly IOrderSyncService _orderSyncService;
    private readonly ILogger<SyncOrdersCommandHandler> _logger;

    public SyncOrdersCommandHandler(
        IOrderSyncService orderSyncService,
        ILogger<SyncOrdersCommandHandler> logger)
    {
        _orderSyncService = orderSyncService;
        _logger = logger;
    }

    public async Task<SyncOrdersResult> Handle(SyncOrdersCommand request, CancellationToken cancellationToken)
    {
        var result = new SyncOrdersResult();

        try
        {
            if (request.OrderIds != null && request.OrderIds.Count > 0)
            {
                // Sync specific orders
                _logger.LogInformation("Syncing {OrderCount} specific orders from SP-API", request.OrderIds.Count);

                foreach (var orderId in request.OrderIds)
                {
                    try
                    {
                        await _orderSyncService.SyncOrderFromSpApiAsync(orderId, cancellationToken);
                        result.OrdersSynced++;
                    }
                    catch (Exception ex)
                    {
                        var error = $"Failed to sync order {orderId}: {ex.Message}";
                        result.Errors.Add(error);
                        _logger.LogError(ex, "Failed to sync order {OrderId}", orderId);
                    }
                }

                result.Message = $"Synced {result.OrdersSynced} out of {request.OrderIds.Count} orders";
            }
            else
            {
                // Sync recent orders
                var syncFromDate = request.SyncFromDate ?? DateTime.UtcNow.AddDays(-30);
                _logger.LogInformation("Syncing orders from SP-API since {FromDate}", syncFromDate);

                await _orderSyncService.SyncRecentOrdersFromSpApiAsync(syncFromDate, cancellationToken);
                
                result.Message = $"Successfully synced orders from {syncFromDate:yyyy-MM-dd}";
                result.OrdersSynced = -1; // Indicate bulk sync (count not tracked)
            }

            result.Success = result.Errors.Count == 0 || (request.OrderIds != null && result.OrdersSynced > 0);

            _logger.LogInformation("Order sync completed. Success: {Success}, Orders synced: {OrdersSynced}, Errors: {ErrorCount}",
                result.Success, result.OrdersSynced, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync orders from SP-API");
            
            result.Success = false;
            result.Errors.Add($"Sync operation failed: {ex.Message}");
            result.Message = "Order synchronization failed";
            
            return result;
        }
    }
}
