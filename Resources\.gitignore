# LSB Seller Central Resources - Documentation and Assets .gitignore

# Temporary files
*.tmp
*.temp
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Editor files
.vscode/
.idea/
*.swp
*.swo

# Backup files
*.backup
*.bak
*.orig

# Generated documentation
docs/build/
docs/generated/
*.generated.*

# Cache files
.cache/
cache/

# Logs
*.log
logs/

# Compiled documentation
*.pdf.tmp
*.docx.tmp

# Image processing temporary files
*.psd.tmp
*.ai.tmp

# Video/Audio temporary files
*.mov.tmp
*.mp4.tmp
*.avi.tmp
*.mp3.tmp
*.wav.tmp

# Archive temporary files
*.zip.tmp
*.rar.tmp
*.7z.tmp

# Local configuration
local.config
*.local

# Sensitive content
private/
confidential/
secrets/

# Large files (consider using Git LFS)
*.iso
*.dmg
*.vdi
*.vmdk

# Development artifacts
test-output/
temp-files/
scratch/

# Auto-generated content
auto-generated/
generated/

# Version control artifacts
.git/
.svn/
.hg/

# Node modules (if any documentation tools use Node)
node_modules/

# Python cache (if any documentation tools use Python)
__pycache__/
*.pyc

# Ruby gems (if any documentation tools use Ruby)
.bundle/
vendor/

# Temporary directories
tmp/
temp/

# Lock files
*.lock

# Environment files
.env
.env.local
.env.*.local

# Database files
*.db
*.sqlite
*.sqlite3

# Certificates and keys
*.pem
*.key
*.crt
*.p12
*.pfx
