namespace LSB.SellerCentral.Core.Authentication;

/// <summary>
/// Interface for SP-API authentication service
/// </summary>
public interface ISpApiAuthenticationService
{
    /// <summary>
    /// Gets the client ID for LWA authentication
    /// </summary>
    string ClientId { get; }

    /// <summary>
    /// Gets the client secret for LWA authentication
    /// </summary>
    string ClientSecret { get; }

    /// <summary>
    /// Gets the refresh token for LWA authentication
    /// </summary>
    string RefreshToken { get; }

    /// <summary>
    /// Gets the LWA endpoint URI
    /// </summary>
    Uri LwaEndpoint { get; }

    /// <summary>
    /// Gets a fresh access token from LWA
    /// </summary>
    /// <returns>Access token</returns>
    Task<string> GetAccessTokenAsync();

    /// <summary>
    /// Validates if the current credentials are properly configured
    /// </summary>
    /// <returns>True if credentials are valid, false otherwise</returns>
    bool ValidateCredentials();

    /// <summary>
    /// Refreshes the access token if needed
    /// </summary>
    /// <returns>True if token was refreshed successfully, false otherwise</returns>
    Task<bool> RefreshTokenIfNeededAsync();
}
